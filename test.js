const axios = require("axios");
const { HttpsProxyAgent } = require("https-proxy-agent");
const { BackWoodApi } = require("./BackWoodApi");
const init = async () => {
  const proxy = "http://namnguyen:<EMAIL>:32028";
  let test = new BackWoodApi({
    baseUrl: "https://launcher.backwoods.gg/v1",
    proxy: proxy,
  });

  await test.authenticate(
    "4VQpRpnzjt2XJMMcAfjFhMbYCjVv74xqjtiTnCkkAQfwaTCtpUKGv7xW1jJ4UxG8tMuLHJQTKrWJ8jQMLCpUGNfu"
  );
  let me = await test.me();
  console.log(me, "me");
  let lauch = await test.launch();
  console.log(lauch, "lauch");
};

init();
