<html><head>    <meta charset="utf-8">
    <meta content="width=device-width,initial-scale=1,maximum-scale=3" name="viewport">

    <!-- section:seometa -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Discord">
    <meta property="og:title" content="Discord - Group Chat That’s All Fun &amp; Games">
    <meta property="og:description" content="Discord is great for playing games and chilling with friends, or even building a worldwide community. Customize your own space to talk, play, and hang out.">
    <meta property="og:image" content="https://cdn.discordapp.com/assets/og_img_discord_home.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@discord">
    <meta name="twitter:creator" content="@discord">
    <!-- endsection --><script nonce="">window.GLOBAL_ENV = {
      API_ENDPOINT: '//discord.com/api',
      API_VERSION: 9,
      GATEWAY_ENDPOINT: 'wss://gateway.discord.gg',
      WEBAPP_ENDPOINT: '//discord.com',
      CDN_HOST: 'cdn.discordapp.com',
      ASSET_ENDPOINT: '//discord.com',
      MEDIA_PROXY_ENDPOINT: '//media.discordapp.net',
      WIDGET_ENDPOINT: '//discord.com/widget',
      INVITE_HOST: 'discord.gg',
      GUILD_TEMPLATE_HOST: 'discord.new',
      GIFT_CODE_HOST: 'discord.gift',
      RELEASE_CHANNEL: 'stable',
      DEVELOPERS_ENDPOINT: '//discord.com',
      MARKETING_ENDPOINT: '//discord.com',
      BRAINTREE_KEY: 'production_ktzp8hfp_49pp2rp4phym7387',
      STRIPE_KEY: 'pk_live_CUQtlpQUF0vufWpnpUmQvcdi',
      ADYEN_KEY: 'live_E3OQ33V6GVGTXOVQZEAFQJ6DJIDVG6SY',
      NETWORKING_ENDPOINT: '//router.discordapp.net',
      RTC_LATENCY_ENDPOINT: '//latency.discord.media/rtc',
      ACTIVITY_APPLICATION_HOST: 'discordsays.com',
      PROJECT_ENV: 'production',
      REMOTE_AUTH_ENDPOINT: '//remote-auth-gateway.discord.gg',
      SENTRY_TAGS: {"buildId":"4058dd9237f1eb4c75219358ead1e0590a47244d","buildType":"normal"},
      MIGRATION_SOURCE_ORIGIN: 'https://discordapp.com',
      MIGRATION_DESTINATION_ORIGIN: 'https://discord.com',
      HTML_TIMESTAMP: Date.now(),
      ALGOLIA_KEY: 'aca0d7082e4e63af5ba5917d5e96bed0',
      PUBLIC_PATH: '/assets/'
    };</script><script nonce="">!function(){if(null==window.WebSocket)return;var n=function(n){try{var o=localStorage.getItem(n);return null==o?null:JSON.parse(o)}catch(n){return null}};if(!n("token")||window.__OVERLAY__)return;var o=null!=window.DiscordNative||null!=window.require?"etf":"json",e=window.GLOBAL_ENV.GATEWAY_ENDPOINT+"/?encoding="+o+"&v="+window.GLOBAL_ENV.API_VERSION;"true"===n("zstd_fast_connect")&&null!=window.DiscordNative&&void 0!==window.Uint8Array&&void 0!==window.TextDecoder?e+="&compress=zstd-stream":void 0!==window.Uint8Array&&(e+="&compress=zlib-stream"),console.log("[FAST CONNECT] "+e+", encoding: "+o+", version: "+window.GLOBAL_ENV.API_VERSION);var i=new WebSocket(e);i.binaryType="arraybuffer";var r=Date.now(),t={open:!1,identify:!1,gateway:e,messages:[]};i.onopen=function(){console.log("[FAST CONNECT] connected in "+(Date.now()-r)+"ms"),t.open=!0},i.onclose=i.onerror=function(){window._ws=null},i.onmessage=function(n){t.messages.push(n)},window._ws={ws:i,state:t}}()</script>    <!-- section:title -->
    <title>Discord</title>
    <!-- endsection -->
  <link rel="icon" href="/assets/favicon.ico"><link href="/assets/12633.18ce18d8842ec9020d22.css" rel="stylesheet"><style type="text/css">.SpriteCanvas-module_spriteCanvasHidden__ndzQV {
  display: none;
  position: absolute;
  width: 0;
  height: 0;
  left: -100%;
}
</style><script charset="utf-8" data-webpack="discord_app:chunk-32640" src="/assets/6b67d4740c31b044828b.js"></script><link data-webpack="discord_app:chunk-46369" data-webpack-loading="1" rel="stylesheet" href="/assets/2917679ca8a08c390036.css"><script charset="utf-8" data-webpack="discord_app:chunk-46369" src="/assets/d1c1376dfbd1c8d5da84.js"></script></head>
  <body>
    <div id="app-mount" class="appMount_ea7e65"><svg viewBox="0 0 1 1" aria-hidden="true" style="position: absolute; pointer-events: none; top: -1px; left: -1px; width: 1px; height: 1px;"><mask id="svg-mask-squircle" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><path fill="white" d="M0 0.464C0 0.301585 0 0.220377 0.0316081 0.158343C0.0594114 0.103776 0.103776 0.0594114 0.158343 0.0316081C0.220377 0 0.301585 0 0.464 0H0.536C0.698415 0 0.779623 0 0.841657 0.0316081C0.896224 0.0594114 0.940589 0.103776 0.968392 0.158343C1 0.220377 1 0.301585 1 0.464V0.536C1 0.698415 1 0.779623 0.968392 0.841657C0.940589 0.896224 0.896224 0.940589 0.841657 0.968392C0.779623 1 0.698415 1 0.536 1H0.464C0.301585 1 0.220377 1 0.158343 0.968392C0.103776 0.940589 0.0594114 0.896224 0.0316081 0.841657C0 0.779623 0 0.698415 0 0.536V0.464Z"></path></mask><mask id="svg-mask-header-bar-badge" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="1" height="1"></rect><circle fill="black" cx="0.75" cy="0.75" r="0.25"></circle></mask><mask id="svg-mask-voice-user-summary-item" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" width="1" height="1"></rect><circle fill="black" cx="1.2083333333333333" cy="0.5" r="0.5416666666666666"></circle></mask><mask id="svg-mask-vertical-fade" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><linearGradient id="svg-mask-vertical-fade-gradient" gradientTransform="rotate(90)" x1="0" x2="1" y1="0" y2="0"><stop offset="0%" stop-color="white"></stop><stop offset="100%" stop-color="black"></stop></linearGradient><rect fill="url(#svg-mask-vertical-fade-gradient)" x="0" y="0" width="1" height="1"></rect></mask><mask id="svg-mask-panel-button" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="1" height="1"></rect><circle fill="black" cx="0.75" cy="0.75" r="0.25"></circle></mask><mask id="svg-mask-channel-call-control-button" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.8214285714285714" cy="0.8214285714285714" r="0.25"></circle></mask><mask id="svg-mask-channel-call-control-button-badge-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6428571428571429" y="-0.07142857142857142" width="0.42857142857142855" height="0.42857142857142855" rx="0.21428571428571427" ry="0.21428571428571427"></rect></mask><mask id="svg-mask-channel-call-control-button-badge-22" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.5357142857142857" y="-0.07142857142857142" width="0.5357142857142857" height="0.42857142857142855" rx="0.21428571428571427" ry="0.21428571428571427"></rect></mask><mask id="svg-mask-channel-call-control-button-badge-29" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.4107142857142857" y="-0.07142857142857142" width="0.6607142857142857" height="0.42857142857142855" rx="0.21428571428571427" ry="0.21428571428571427"></rect></mask><mask id="svg-mask-avatar-default" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle></mask><mask id="svg-mask-avatar-status-round-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.8125" cy="0.8125" r="0.3125"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7604166666666667" cy="0.7604166666666667" r="0.2604166666666667"></circle></mask><mask id="svg-mask-avatar-status-mobile-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.5" y="0.3125" width="0.625" height="0.8125" rx="0.1625" ry="0.1625"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.5" y="0.34375" width="0.5208333333333334" height="0.6770833333333334" rx="0.1625" ry="0.1625"></rect></mask><mask id="svg-mask-avatar-status-typing-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.21875" y="0.5" width="1.1875" height="0.625" rx="0.3125" ry="0.3125"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.265625" y="0.5" width="0.9895833333333334" height="0.5208333333333334" rx="0.0968967013888889" ry="0.0968967013888889"></rect></mask><mask id="svg-mask-avatar-status-round-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.25"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7916666666666667" cy="0.7916666666666667" r="0.20833333333333334"></circle></mask><mask id="svg-mask-avatar-status-mobile-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6" y="0.45" width="0.5" height="0.65" rx="0.13" ry="0.13"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.5833333333333333" y="0.45833333333333337" width="0.4166666666666667" height="0.5416666666666667" rx="0.13" ry="0.13"></rect></mask><mask id="svg-mask-avatar-status-typing-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.375" y="0.6" width="0.95" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.3958333333333333" y="0.5833333333333333" width="0.7916666666666666" height="0.4166666666666667" rx="0.0920138888888889" ry="0.0920138888888889"></rect></mask><mask id="svg-mask-avatar-status-round-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.8333333333333334" cy="0.8333333333333334" r="0.2916666666666667"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7777777777777779" cy="0.7777777777777779" r="0.24305555555555558"></circle></mask><mask id="svg-mask-avatar-status-mobile-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.5416666666666666" y="0.375" width="0.5833333333333334" height="0.75" rx="0.15" ry="0.15"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.5347222222222222" y="0.3958333333333333" width="0.48611111111111116" height="0.625" rx="0.15" ry="0.15"></rect></mask><mask id="svg-mask-avatar-status-typing-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.2916666666666667" y="0.5416666666666666" width="1.0833333333333333" height="0.5833333333333334" rx="0.2916666666666667" ry="0.2916666666666667"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.3263888888888889" y="0.5347222222222222" width="0.9027777777777778" height="0.48611111111111116" rx="0.091772762345679" ry="0.091772762345679"></rect></mask><mask id="svg-mask-avatar-status-round-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.84375" cy="0.84375" r="0.25"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7864583333333334" cy="0.7864583333333334" r="0.20833333333333334"></circle></mask><mask id="svg-mask-avatar-status-mobile-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.59375" y="0.4375" width="0.5" height="0.65625" rx="0.13125" ry="0.13125"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.578125" y="0.4479166666666667" width="0.4166666666666667" height="0.546875" rx="0.13125" ry="0.13125"></rect></mask><mask id="svg-mask-avatar-status-typing-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.359375" y="0.59375" width="0.96875" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.3828125" y="0.578125" width="0.8072916666666667" height="0.4166666666666667" rx="0.08875868055555558" ry="0.08875868055555558"></rect></mask><mask id="svg-mask-avatar-status-round-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.25"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7916666666666667" cy="0.7916666666666667" r="0.20833333333333334"></circle></mask><mask id="svg-mask-avatar-status-mobile-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6" y="0.45" width="0.5" height="0.65" rx="0.13" ry="0.13"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.5833333333333333" y="0.45833333333333337" width="0.4166666666666667" height="0.5416666666666667" rx="0.13" ry="0.13"></rect></mask><mask id="svg-mask-avatar-status-typing-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.375" y="0.6" width="0.95" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.3958333333333333" y="0.5833333333333333" width="0.7916666666666666" height="0.4166666666666667" rx="0.0876736111111111" ry="0.0876736111111111"></rect></mask><mask id="svg-mask-avatar-status-round-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.875" cy="0.875" r="0.20833333333333334"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.8125" cy="0.8125" r="0.17361111111111113"></circle></mask><mask id="svg-mask-avatar-status-mobile-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6666666666666666" y="0.5416666666666666" width="0.4166666666666667" height="0.5416666666666666" rx="0.10833333333333334" ry="0.10833333333333334"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.6388888888888888" y="0.5347222222222222" width="0.34722222222222227" height="0.4513888888888889" rx="0.10833333333333334" ry="0.10833333333333334"></rect></mask><mask id="svg-mask-avatar-status-typing-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.4791666666666667" y="0.6666666666666666" width="0.7916666666666666" height="0.4166666666666667" rx="0.20833333333333334" ry="0.20833333333333334"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.4826388888888889" y="0.6388888888888888" width="0.6597222222222222" height="0.34722222222222227" rx="0.08634741512345678" ry="0.08634741512345678"></rect></mask><mask id="svg-mask-avatar-status-round-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.8392857142857143" cy="0.8392857142857143" r="0.19642857142857142"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7827380952380953" cy="0.7827380952380953" r="0.1636904761904762"></circle></mask><mask id="svg-mask-avatar-status-mobile-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6428571428571429" y="0.5178571428571429" width="0.39285714285714285" height="0.5178571428571429" rx="0.10357142857142858" ry="0.10357142857142858"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.6190476190476191" y="0.5148809523809524" width="0.3273809523809524" height="0.4315476190476191" rx="0.10357142857142858" ry="0.10357142857142858"></rect></mask><mask id="svg-mask-avatar-status-typing-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.45535714285714285" y="0.6428571428571429" width="0.7678571428571429" height="0.39285714285714285" rx="0.19642857142857142" ry="0.19642857142857142"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.46279761904761907" y="0.6190476190476191" width="0.6398809523809524" height="0.3273809523809524" rx="0.08576920351473921" ry="0.08576920351473921"></rect></mask><mask id="svg-mask-avatar-status-round-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.175"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7916666666666667" cy="0.7916666666666667" r="0.14583333333333334"></circle></mask><mask id="svg-mask-avatar-decoration-profile-status-square-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7916666666666667" cy="0.7916666666666667" r="0.14583333333333334"></circle></mask><mask id="svg-mask-avatar-status-mobile-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.675" y="0.575" width="0.35" height="0.45" rx="0.09" ry="0.09"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.6458333333333334" y="0.5625" width="0.2916666666666667" height="0.375" rx="0.09" ry="0.09"></rect></mask><mask id="svg-mask-avatar-decoration-profile-status-mobile-square-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.6458333333333334" y="0.5625" width="0.2916666666666667" height="0.375" rx="0.09" ry="0.09"></rect></mask><mask id="svg-mask-avatar-status-typing-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.525" y="0.675" width="0.65" height="0.35" rx="0.175" ry="0.175"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.5208333333333334" y="0.6458333333333334" width="0.5416666666666667" height="0.2916666666666667" rx="0.08485243055555552" ry="0.08485243055555552"></rect></mask><mask id="svg-mask-avatar-status-round-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.8333333333333334" cy="0.8333333333333334" r="0.16666666666666666"></circle></mask><mask id="svg-mask-avatar-decoration-status-round-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="0.7777777777777779" cy="0.7777777777777779" r="0.1388888888888889"></circle></mask><mask id="svg-mask-avatar-status-mobile-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.6666666666666666" y="0.5666666666666667" width="0.3333333333333333" height="0.43333333333333335" rx="0.08666666666666667" ry="0.08666666666666667"></rect></mask><mask id="svg-mask-avatar-decoration-status-mobile-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.6388888888888888" y="0.5555555555555556" width="0.2777777777777778" height="0.36111111111111116" rx="0.08666666666666667" ry="0.08666666666666667"></rect></mask><mask id="svg-mask-avatar-status-typing-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.5166666666666667" y="0.6666666666666666" width="0.6333333333333333" height="0.3333333333333333" rx="0.16666666666666666" ry="0.16666666666666666"></rect></mask><mask id="svg-mask-avatar-decoration-status-typing-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><rect fill="black" x="0.513888888888889" y="0.6388888888888888" width="0.5277777777777778" height="0.2777777777777778" rx="0.08429783950617281" ry="0.08429783950617281"></rect></mask><mask id="svg-mask-diagonal-facepile-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.5208333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle></mask><mask id="svg-mask-diagonal-facepile-status-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.5208333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><circle fill="black" cx="0.8125" cy="0.8125" r="0.3125"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-16" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.5208333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><rect fill="black" x="0.21875" y="0.5" width="1.1875" height="0.625" rx="0.3125" ry="0.3125"></rect></mask><mask id="svg-mask-diagonal-facepile-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4833333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle></mask><mask id="svg-mask-diagonal-facepile-status-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4833333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.25"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-20" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4833333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><rect fill="black" x="0.375" y="0.6" width="0.95" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-diagonal-facepile-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4583333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle></mask><mask id="svg-mask-diagonal-facepile-status-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4583333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><circle fill="black" cx="0.8333333333333334" cy="0.8333333333333334" r="0.2916666666666667"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-24" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4583333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><rect fill="black" x="0.2916666666666667" y="0.5416666666666666" width="1.0833333333333333" height="0.5833333333333334" rx="0.2916666666666667" ry="0.2916666666666667"></rect></mask><mask id="svg-mask-diagonal-facepile-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4270833333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle></mask><mask id="svg-mask-diagonal-facepile-status-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4270833333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><circle fill="black" cx="0.84375" cy="0.84375" r="0.25"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.4270833333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><rect fill="black" x="0.359375" y="0.59375" width="0.96875" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-diagonal-facepile-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4083333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle></mask><mask id="svg-mask-diagonal-facepile-status-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4083333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.25"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-40" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.4083333333333334"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><rect fill="black" x="0.375" y="0.6" width="0.95" height="0.5" rx="0.25" ry="0.25"></rect></mask><mask id="svg-mask-diagonal-facepile-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3958333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle></mask><mask id="svg-mask-diagonal-facepile-status-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3958333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><circle fill="black" cx="0.875" cy="0.875" r="0.20833333333333334"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-48" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3958333333333333"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><rect fill="black" x="0.4791666666666667" y="0.6666666666666666" width="0.7916666666666666" height="0.4166666666666667" rx="0.20833333333333334" ry="0.20833333333333334"></rect></mask><mask id="svg-mask-diagonal-facepile-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.3869047619047619"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle></mask><mask id="svg-mask-diagonal-facepile-status-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.3869047619047619"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><circle fill="black" cx="0.8392857142857143" cy="0.8392857142857143" r="0.19642857142857142"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-56" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.3869047619047619"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><rect fill="black" x="0.45535714285714285" y="0.6428571428571429" width="0.7678571428571429" height="0.39285714285714285" rx="0.19642857142857142" ry="0.19642857142857142"></rect></mask><mask id="svg-mask-diagonal-facepile-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.37083333333333335"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle></mask><mask id="svg-mask-diagonal-facepile-status-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.37083333333333335"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.175"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.33333333333333337" cy="0.33333333333333337" r="0.33333333333333337"></circle><circle fill="black" cx="0.6666666666666667" cy="0.6666666666666667" r="0.37083333333333335"></circle><circle fill="white" cx="0.6666666666666667" cy="0.6666666666666667" r="0.33333333333333337"></circle><rect fill="black" x="0.525" y="0.675" width="0.65" height="0.35" rx="0.175" ry="0.175"></rect></mask><mask id="svg-mask-diagonal-facepile-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.35833333333333334"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle></mask><mask id="svg-mask-diagonal-facepile-status-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.35833333333333334"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><circle fill="black" cx="0.8333333333333334" cy="0.8333333333333334" r="0.16666666666666666"></circle></mask><mask id="svg-mask-diagonal-facepile-typing-120" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.3333333333333333" cy="0.3333333333333333" r="0.3333333333333333"></circle><circle fill="black" cx="0.6666666666666666" cy="0.6666666666666666" r="0.35833333333333334"></circle><circle fill="white" cx="0.6666666666666666" cy="0.6666666666666666" r="0.3333333333333333"></circle><rect fill="black" x="0.5166666666666667" y="0.6666666666666666" width="0.6333333333333333" height="0.3333333333333333" rx="0.16666666666666666" ry="0.16666666666666666"></rect></mask><mask id="svg-mask-status-online-mobile" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="1" height="1" rx="0.1875" ry="0.125"></rect><rect fill="black" x="0.125" y="0.16666666666666666" width="0.75" height="0.5"></rect><ellipse fill="black" cx="0.5" cy="0.8333333333333334" rx="0.125" ry="0.08333333333333333"></ellipse></mask><mask id="svg-mask-status-online" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle></mask><mask id="svg-mask-status-idle" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.25" cy="0.25" r="0.375"></circle></mask><mask id="svg-mask-status-dnd" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><rect fill="black" x="0.125" y="0.375" width="0.75" height="0.25" rx="0.125" ry="0.125"></rect></mask><mask id="svg-mask-status-offline" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.5" cy="0.5" r="0.25"></circle></mask><mask id="svg-mask-status-streaming" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><polygon fill="black" points="0.35,0.25 0.78301275,0.5 0.35,0.75"></polygon></mask><mask id="svg-mask-status-typing" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" cx="0" cy="0" width="1" height="1" ry="0.5" rx="0.2"></rect></mask><mask id="svg-mask-status-screenshare" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect width="1" height="1" fill="white"></rect><path d="M0.5 0.71875C0.5 0.649716 0.555966 0.59375 0.625 0.59375H1.0V1.0H0.5V0.71875Z" fill="black"></path></mask><mask id="svg-mask-avatar-voice-call-80" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5"></circle><circle fill="black" cx="0.85" cy="0.85" r="0.2"></circle></mask><mask id="svg-mask-avatar-call-icon" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5" opacity="1"></circle><circle fill="black" cx="0.85" cy="0.15" r="0.2"></circle></mask><mask id="svg-mask-avatar-call-icon-32" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><circle fill="white" cx="0.5" cy="0.5" r="0.5" opacity="0.5"></circle><circle fill="black" cx="0.8" cy="0.25" r="0.325"></circle></mask><mask id="svg-mask-sticker-rounded-rect" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><path d="M0 0.26087C0 0.137894 0 0.0764069 0.0382035 0.0382035C0.0764069 0 0.137894 0 0.26087 0H0.73913C0.862106 0 0.923593 0 0.961797 0.0382035C1 0.0764069 1 0.137894 1 0.26087V0.73913C1 0.862106 1 0.923593 0.961797 0.961797C0.923593 1 0.862106 1 0.73913 1H0.26087C0.137894 1 0.0764069 1 0.0382035 0.961797C0 0.923593 0 0.862106 0 0.73913V0.26087Z" fill="white"></path></mask><mask id="svg-mask-chat-input-button-notification" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect width="1" height="1" fill="white"></rect><circle cx="0.85" cy="0.85" r="0.25" fill="black"></circle></mask><mask id="svg-mask-sticker-shop-notification" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect width="1" height="1" fill="white"></rect><circle cx="0.9" cy="0.9" r="0.5" fill="black"></circle></mask><mask id="svg-mask-autocomplete-emoji-upsell-emoji" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" width="1" height="1"></rect><circle fill="black" cx="1.33" cy="0.5" r="0.5833333333333334"></circle></mask><mask id="svg-mask-event-ticket" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><path d="M0 0.12C0 0.0779961 0 0.0569941 0.00408726 0.0409507C0.00768251 0.0268386 0.0134193 0.015365 0.0204754 0.00817451C0.028497 0 0.038998 0 0.06 0H0.94C0.961002 0 0.971503 0 0.979525 0.00817451C0.986581 0.015365 0.992318 0.0268386 0.995913 0.0409507C1 0.0569941 1 0.0779961 1 0.12V0.45C0.986193 0.45 0.975 0.472386 0.975 0.5C0.975 0.527614 0.986193 0.55 1 0.55V0.88C1 0.922004 1 0.943006 0.995913 0.959049C0.992318 0.973161 0.986581 0.984635 0.979525 0.991826C0.971503 1 0.961002 1 0.94 1H0.0600001C0.0389981 1 0.028497 1 0.0204754 0.991826C0.0134193 0.984635 0.00768251 0.973161 0.00408726 0.959049C0 0.943006 0 0.922004 0 0.88V0.55C0.0138071 0.55 0.025 0.527614 0.025 0.5C0.025 0.472386 0.0138071 0.45 0 0.45V0.12Z" fill="white"></path></mask><mask id="svg-mask-guild-icon-with-channel-type" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><path fill="white" d="M0 0.464C0 0.301585 0 0.220377 0.0316081 0.158343C0.0594114 0.103776 0.103776 0.0594114 0.158343 0.0316081C0.220377 0 0.301585 0 0.464 0H0.536C0.698415 0 0.779623 0 0.841657 0.0316081C0.896224 0.0594114 0.940589 0.103776 0.968392 0.158343C1 0.220377 1 0.301585 1 0.464V0.536C1 0.698415 1 0.779623 0.968392 0.841657C0.940589 0.896224 0.896224 0.940589 0.841657 0.968392C0.779623 1 0.698415 1 0.536 1H0.464C0.301585 1 0.220377 1 0.158343 0.968392C0.103776 0.940589 0.0594114 0.896224 0.0316081 0.841657C0 0.779623 0 0.698415 0 0.536V0.464Z"></path><circle cx="0.9" cy="0.9" r="0.5" fill="black"></circle></mask><mask id="svg-mask-content-inventory-card-face-pile-avatar" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" width="1" height="1"></rect><circle fill="black" cx="1.33" cy="0.5" r="0.65"></circle></mask><mask id="svg-mask-guild-popout-activity-icon" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" x="0" y="0" width="100%" height="100%"></rect><circle fill="black" cx="1.33" cy="0.5" r="0.6"></circle></mask><mask id="svg-mask-clan-icon" maskContentUnits="objectBoundingBox" viewBox="0 0 1 1"><rect fill="white" rx="0.3" ry="0.3" width="1" height="1"></rect></mask></svg><svg viewBox="0 0 1 1" aria-hidden="true" style="position: absolute; pointer-events: none; top: -1px; left: -1px; width: 1px; height: 1px;"><linearGradient id="b7b64754-43de-4a5c-b1ec-a1da146bd394"><stop offset=".1762" stop-color="var(--premium-tier-0-blue-for-gradients)"></stop><stop offset="0.5351" stop-color="var(--premium-tier-0-blue-for-gradients-2)"></stop><stop offset="1" stop-color="var(--premium-tier-0-purple-for-gradients)"></stop></linearGradient><linearGradient id="a3a0023e-af42-46f1-a236-5b708532b601"><stop stop-color="var(--premium-tier-1-blue)"></stop><stop offset="1" stop-color="var(--premium-tier-1-purple)"></stop></linearGradient><linearGradient id="6c590762-0c20-4889-a4ec-6b15d9c4bfd5"><stop stop-color="var(--premium-tier-2-purple-for-gradients)"></stop><stop offset="0.502368" stop-color="var(--premium-tier-2-purple-for-gradients-2)"></stop><stop offset="1" stop-color="var(--premium-tier-2-pink-for-gradients)"></stop></linearGradient><linearGradient id="9e92a540-0a97-41c4-839b-628c2d47f5e7"><stop stop-color="var(--guild-boosting-blue)"></stop><stop offset="1" stop-color="var(--guild-boosting-purple)"></stop></linearGradient><linearGradient id="fa93f0b4-ff9a-4f7e-86e5-8ff2ffa01242" gradientTransform="rotate(45)"><stop offset="0" stop-color="var(--premium-tier-2-purple)"></stop><stop offset="1" stop-color="var(--premium-tier-2-pink)"></stop></linearGradient></svg><div style="position: fixed; opacity: 0; pointer-events: none;"></div><div class="appAsidePanelWrapper_bd26cc"><div class="notAppAsidePanel_bd26cc" data-app-not-dev-tools="true"><div class="app_bd26cc"><div style="position: absolute; width: 100%; height: 100%; background-color: transparent;"></div></div><div id="cash-app-pay-container"></div><div></div><div class="layerContainer_cd0de5"></div><div class="layerContainer_cd0de5"></div><div class="containerTop_b18d5c"></div><div style="position: absolute; width: 100%; height: 100%; background-color: transparent;"></div><canvas class="canvas_a41a9d"></canvas><canvas class="SpriteCanvas-module_spriteCanvasHidden__ndzQV" width="240" height="240"></canvas></div><div class="pictureInPicture_d0596b"></div></div><div style="position: fixed; opacity: 0; pointer-events: none;"></div><div></div></div><script nonce="">window.__OVERLAY__=/overlay/.test(location.pathname)</script><script nonce="">window.__BILLING_STANDALONE__=/^\/billing/.test(location.pathname)</script>  

<script src="/assets/web.3baf0a0ff45cca9abbdf.js" defer=""></script><script src="/assets/sentry.c431684f5d3be9f51bd9.js" defer=""></script><script nonce="">(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.nonce='MjA4LDIwMCwxNDEsODcsMjAxLDEzMiw2NiwxNzk=';d.innerHTML="window.__CF$cv$params={r:'8a32301ef9ab853a',t:'************************'};var a=document.createElement('script');a.nonce='MjA4LDIwMCwxNDEsODcsMjAxLDEzMiw2NiwxNzk=';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"></iframe><div class="drag-previewer" style="z-index: 1000; filter: drop-shadow(0 0 0 var(--background-floating)) drop-shadow(var(--elevation-high));"><svg style="contain: paint;"><foreignobject></foreignobject></svg></div><div id="uid_1" style="display: none;">,</div><div id="uid_2" style="display: none;">,</div><div id="uid_3" style="display: none;">Reactions</div><div id="uid_4" style="display: none;">click to open image dialog</div></body></html>