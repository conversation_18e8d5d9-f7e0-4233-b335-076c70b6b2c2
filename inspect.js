// app.js
const { System } = require("systemjs");
const Proxy = require("http-mitm-proxy").Proxy;
const proxy = new Proxy();
const fs = require("fs");

const intToBytes = (n) => {
  return [255 & n, (n >> 8) & 255];
};

const bytesToInt = (n) => {
  return n[0] | (n[1] << 8);
};

proxy.onWebSocketConnection((ctx, callback) => {
  console.log(
    "WEBSOCKET CONNECT:",
    ctx.clientToProxyWebSocket.upgradeReq.headers.host
  );
  //
  return callback();
});

// Example of dynamically importing a module
const init = async () => {
  let MessageIdProtocol = (
    await System.import("./game_sources/MessageIdProtocol.js")
  ).default;

  proxy.onWebSocketMessage((ctx, message, flags, callback) => {
    if (
      !ctx.clientToProxyWebSocket.upgradeReq.headers.host.includes("backwoods")
    ) {
      return callback(null, message, flags);
    }

    // console.log(message, "messageByte");
    // console.log(MessageIdProtocol);
    let o = bytesToInt(message.slice(0, 2));
    let captcha = bytesToInt(message.slice(2, 4));
    var c = message.slice(4);
    var r = MessageIdProtocol.get(o).decode(c);

    let dataMess = JSON.stringify(r) + " - RECEIVE - " + o + "\n";
    fs.appendFileSync("inspect.txt", dataMess);
    console.log(dataMess);
    //
    // console.log(r);
    //   console.log(decodeBinaryMessage(messageByte));
    //   let data = bytesToInt(s.slice(0, 2));
    //   let captcha = bytesToInt(s.slice(2, 4));
    //   //

    //   console.log(
    //     `WEBSOCKET FRAME ${type} received from ${fromServer ? "server" : "client"}`,
    //     ctx.clientToProxyWebSocket.upgradeReq.url,
    //     message
    //   );
    //
    return callback(null, message, flags);
  });

  proxy.onWebSocketSend(function (ctx, message, flags, callback) {
    if (
      !ctx.clientToProxyWebSocket.upgradeReq.headers.host.includes("backwoods")
    ) {
      return callback(null, message, flags);
    }

    let o = bytesToInt(message.slice(0, 2));
    let captcha = bytesToInt(message.slice(2, 4));
    var c = message.slice(4);
    var r = MessageIdProtocol.get(o).decode(c);
    let dataMess = JSON.stringify(r) + " - SEND - " + o + "\n";
    fs.appendFileSync("inspect.txt", dataMess);
    console.log(dataMess);
    return callback(null, message, flags);
  });
  // proxy.onWebSocketError(function (ctx, err) {
  //   console.log("WEBSOCKET ERROR:", err);
  // });
  // proxy.onWebSocketClose(function (ctx, code, message, callback) {
  //   console.log("WEBSOCKET CLOSE:", code);
  //   let o = bytesToInt(message.slice(0, 2));
  //   let captcha = bytesToInt(message.slice(2, 4));
  //   var c = message.slice(4);
  //   console.log(o, "o");
  //   var r = MessageIdProtocol.get(o).decode(c);
  //   let dataMess = JSON.stringify(r) + " - SEND - " + o + "\n";
  // });
  console.log("begin listening on 8081");
  proxy.listen({ port: 8081, sslCaDir: process.cwd() + "/http-mitm-proxy" });
};

init();
