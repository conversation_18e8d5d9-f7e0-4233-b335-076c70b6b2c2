{"name": "blackwood", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"inspect": "nodemon inspect.js", "app": "nodemon"}, "author": "", "license": "ISC", "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@project-serum/anchor": "^0.26.0", "@solana/spl-token": "^0.4.14", "@solana/web3.js": "^1.95.0", "@stableproxy/puppeteer-page-proxy": "^2.3.2", "@supercharge/promise-pool": "^3.2.0", "@vespaiach/axios-fetch-adapter": "^0.3.1", "axios": "^1.7.2", "bs58": "^6.0.0", "crypto": "^1.0.1", "http-mitm-proxy": "^1.1.0", "https-proxy-agent": "^7.0.5", "js-md5": "^0.8.3", "lodash": "^4.17.21", "node-2fa": "^2.0.3", "node-fetch": "^2.7.0", "nodemon": "^3.1.4", "protobufjs": "^7.3.2", "puppeteer": "^22.13.0", "systemjs": "^6.15.1", "telegraf": "^4.16.3", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "ws": "^8.18.0"}}