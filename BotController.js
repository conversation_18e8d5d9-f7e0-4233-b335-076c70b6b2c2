const md5 = require("js-md5");
const { intToBytes, sleep, getRandomInt } = require("./utils");
const { randomInt } = require("crypto");
const _ = require("lodash");
const { clearInterval } = require("timers");

const GameStatus = {
  INIT: "INIT",
  PLAY: "PLAY",
  VIT_NOT_ENOUGH: "VIT_NOT_ENOUGH",
  CLOSE: "CLOSE",
};

exports.GameStatus = GameStatus;

class BotController {
  gameStatus = null;

  CAPTCHA = 9527;
  ws = null;
  MessageId = null;
  Proto = null;
  MessageIdProtocol = null;

  hearthBeat = 0;
  gameHearthBeat = 0;

  chest = null;
  weapons = [];
  enemies = [];
  address = null;
  gold = 0;
  score = 0;
  hp = 0;
  vit = 0;
  batchAttack = 0;
  wave = 1;

  gameBeatInterval = null;
  battleBeatInterval = null;
  isCollectingChest = false;
  stopGameLoop = true;

  onChangeStatus = null;

  constructor(_ws, _MessageId, _Proto, MessageIdProtocol) {
    this.ws = _ws;
    this.MessageId = _MessageId;
    this.Proto = _Proto;
    this.MessageIdProtocol = MessageIdProtocol;
    this.changeStatus(GameStatus.INIT);
  }

  setOnChangeStatus(_onChangeStatus) {
    this.onChangeStatus = _onChangeStatus.bind(this);
  }
  async changeStatus(status) {
    this.gameStatus = status;
    this.onChangeStatus && this.onChangeStatus(status);
  }

  async LoginLoginSend(otp) {
    let loginData = new this.Proto.msg.LoginLogin();
    loginData.OpenID = "";
    loginData.Code = otp;
    this.send(this.MessageId.LoginLogin, loginData);
    await this.CommonHeartbeatSend();
    this.gameBeatInterval = setInterval(() => {
      this.CommonHeartbeatSend();
    }, [3000]);
  }

  async LoginLoginReceive(data) {
    let result = data.result;
    this.vit = result.Plr.User.Vit ?? 0;
    this.address = result.Plr.User.OpenID ?? "";
    if (!result.Plr.BField) {
      await sleep(3000);
      await this.GameStartSend();
    } else {
      await sleep(2000);
      await this.GameStartReceive_ComeBack({
        result: { Field: result.Plr.BField, User: result.User },
      });

      if (result.Plr.BField.Ctx.State == this.Proto.msg.GameState.GS_Shopping) {
        await sleep(5000);
        await this.GameDoneReceive_ComeBack({
          result: {
            State: result.Plr.BField.Ctx.State,
            Shop: result.Plr.BField.Ctx.Shop,
          },
        });
      } else if (!result.Plr.BField.Ctx.State) {
        await this.GameLoadSend();
      }
    }
  }

  async CommonHandShakeReceive(data) {
    let result = data.result;
    let ts = result.TS;
    let commonHandShake = new this.Proto.msg.CommonHandShake();
    let currTs = Math.floor(Date.now() / 1e3);
    commonHandShake.TS = currTs;
    commonHandShake.Sign = md5(md5(`${ts}_${currTs}_rjsB#dnkA4nN`));

    this.send(this.MessageId.CommonHandShake, commonHandShake);
  }

  async CommonHeartbeatSend() {
    var commonHeartbeat = new this.Proto.msg.CommonHeartbeat();
    this.hearthBeat++;
    commonHeartbeat.Cnt = this.hearthBeat;
    this.send(this.MessageId.CommonHeartbeat, commonHeartbeat);
  }

  async GameStartSend() {
    if (this.vit > 0) {
      var gameStart = new this.Proto.msg.GameStart();
      gameStart.HeroSN = 0;
      gameStart.Chapter = 10100;
      gameStart.Mode = 0;
      this.vit -= 5;
      this.send(this.MessageId.GameStart, gameStart);
    } else {
      this.stop(GameStatus.VIT_NOT_ENOUGH);
    }
  }

  async _GameStartReceive(data) {
    let result = data.result;
    this.weapons = result.Field.Weapons;
    this.hp = result.Field.Ctx.HP ?? result.Field.CharAttr[0];
    this.gold = result.Field.Ctx.Gold ?? 0;
    this.changeStatus(GameStatus.PLAY);
  }

  async GameStartReceive_ComeBack(data) {
    await this._GameStartReceive(data);
  }

  async GameStartReceive(data) {
    await this._GameStartReceive(data);
    await this.GameLoadSend();
  }

  async GameLoadSend() {
    var gameLoad = new this.Proto.msg.GameLoaded();
    this.send(this.MessageId.GameLoaded, gameLoad);
  }

  async GameLoadReceive() {
    this.startGameLoop();
    this.battleBeatInterval = setInterval(() => {
      this.GameHeartbeatSend();
    }, [2000]);
  }

  async GameHeartbeatSend() {
    var gameHeartbeat = new this.Proto.msg.GameHeartbeat();
    this.gameHearthBeat++;
    gameHeartbeat.Cnt = this.gameHearthBeat;
    this.send(this.MessageId.GameHeartbeat, gameHeartbeat);
  }

  async startGameLoop() {
    this.stopGameLoop = false;
    while (true) {
      if (this.stopGameLoop) break;
      await sleep(randomInt(5) * 100);
      await this.GameCharAtkSend();
    }
  }

  async GameCharAtkSend() {
    if (this.enemies.length == 0) return;
    let randomEnemy = _.orderBy(this.enemies, ["hp"], ["asc"])[0];

    let randomWeapon =
      this.weapons[Math.floor(Math.random() * this.weapons.length)];
    let damage = randomInt(1500);
    randomEnemy.hp = randomEnemy.hp - damage;
    this.batchAttack++;

    //remove enemies
    if (randomEnemy.hp <= 0) {
      this.enemies = this.enemies.filter((it) => it.sn != randomEnemy.sn);
    }

    var gameCharAtk = new this.Proto.msg.GameCharAtk();
    gameCharAtk.AOT = "AOT_Weapon";
    gameCharAtk.AOInfo = [
      this.batchAttack,
      0,
      randomWeapon.SN,
      randomWeapon.AtkIntl,
    ];

    let attack = new this.Proto.msg.BattleDmg();
    attack.CrtSN = randomEnemy.sn;
    attack.Dmg = damage;
    attack.Critical = false;
    attack.CriticalRand = getRandomInt(10000);
    attack.Pos = [getRandomInt(400), getRandomInt(400)];
    attack.Knockback = randomWeapon.Knockback;

    gameCharAtk.CltDmg = [attack];

    this.send(this.MessageId.GameCharAtk, gameCharAtk);
  }

  async GameCharAtkReceive(data) {
    let result = data.result;
    if (result.DropItems) {
      result.DropItems.map((it) => (this.gold += it.Val));
    }

    if (result.AddScore) {
      this.score += result.AddScore;
    }
  }

  async GameSpawnReceive(data) {
    let result = data.result;
    result.Spawn.map((it) => {
      if (it.Type) return;
      let enemyObj = { id: it.ID, sn: it.SN, hp: it.CrtAttr[0] };
      this.enemies.push(enemyObj);
    });
  }

  async _GameDoneReceive(data) {
    clearInterval(this.battleBeatInterval);
    let result = data.result;
    this.enemies = [];
    if (result.State == this.Proto.msg.GameState.GS_Win) {
      this.weapons = [];
      this.chest = null;
      this.weapons = [];
      this.enemies = [];
      this.gold = 0;
      this.score = 0;
      this.hp = 0;
      this.batchAttack = 0;
      this.stopGameLoop = true;
      this.wave = 0;
      this.changeStatus(GameStatus.INIT);
      await sleep(5000);
      this.GameStartSend();

      return;
    }
    this.wave++;

    const shop = result.Shop;
    if (shop.ChestItem) {
      await this.GameShopOperateChestCollectSend(shop.ChestItem);
    }
    while (this.isCollectingChest) {
      await sleep(2000);
    }
    for (let i = 0; i < shop.Goods.length; i++) {
      let good = shop.Goods[i];
      if (
        good.Price != -1 &&
        good.Price <= this.gold &&
        good.Type == 1 &&
        this.weapons.length < 6
      ) {
        this.gold -= good.Price;
        let shopOps = new this.Proto.msg.GameShopOperate();
        shopOps.Op = this.Proto.msg.GameShopOpType.GSO_GoodsBuy;
        shopOps.OpVal.push(i);
        this.send(this.MessageId.GameShopOperate, shopOps);
        await sleep(1000);
      }
      //todo implement buy other stuff
    }
    await sleep(1000);
    let shopOps = new this.Proto.msg.GameShopOperate();
    shopOps.Op = this.Proto.msg.GameShopOpType.GSO_GoodsClose;
    this.send(this.MessageId.GameShopOperate, shopOps);
  }

  async GameDoneReceive(data) {
    this._GameDoneReceive(data);
  }

  async GameDoneReceive_ComeBack(data) {
    this._GameDoneReceive(data);
    await sleep(1000);
    this.startGameLoop();
  }

  async GameShopOperateChestCollectSend(chestId) {
    this.isCollectingChest = true;
    let shopOps = new this.Proto.msg.GameShopOperate();
    shopOps.Op = this.Proto.msg.GameShopOpType.GSO_ChestCollect;
    shopOps.OpVal.push(chestId);
    this.send(this.MessageId.GameShopOperate, shopOps);
  }

  async GameShopOperateReceive(data) {
    let result = data.result;
    if (result.Op == this.Proto.msg.GameShopOpType.GSO_ChestCollect) {
      if (result.ChestItem) {
        await this.GameShopOperateChestCollectSend(result.ChestItem);
      } else {
        this.isCollectingChest = false;
      }
    }

    if (result.Op == this.Proto.msg.GameShopOpType.GSO_GoodsBuy) {
      if (result.Update.WAdd) {
        this.weapons.push(...result.Update.WAdd);
      }

      //todo implement update w from other stuff
    }

    if (result.Op == this.Proto.msg.GameShopOpType.GSO_GoodsClose) {
      this.battleBeatInterval = setInterval(() => {
        this.GameHeartbeatSend();
      }, [2000]);
    }
  }

  async stop(gameStatus = GameStatus.CLOSE) {
    this.changeStatus(gameStatus);
    this.stopGameLoop = true;
    clearInterval(this.gameBeatInterval);
    clearInterval(this.battleBeatInterval);
  }

  async runner(actionCode, data) {
    switch (actionCode) {
      case this.MessageId.LoginLogin:
        return this.LoginLoginReceive(data);
      case this.MessageId.CommonHandShake:
        return this.CommonHandShakeReceive(data);
      case this.MessageId.GameStart:
        return this.GameStartReceive(data);
      case this.MessageId.GameLoaded:
        return this.GameLoadReceive(data);
      case this.MessageId.GameSpawn:
        return this.GameSpawnReceive(data);
      case this.MessageId.GameCharAtk:
        return this.GameCharAtkReceive(data);
      case this.MessageId.GameDone:
        return this.GameDoneReceive(data);
      case this.MessageId.GameShopOperate:
        return this.GameShopOperateReceive(data);
      default:
        return null;
    }
  }

  send(actionCode, data) {
    let actionCodeByte = intToBytes(actionCode);
    let captchaByte = intToBytes(this.CAPTCHA);
    let dataBuff = this.MessageIdProtocol.get(actionCode).encode(data).finish();
    // console.log(JSON.stringify(data), "SEND", actionCode);

    const result = new Uint8Array(
      actionCodeByte.length + captchaByte.length + dataBuff.length
    );
    result.set(actionCodeByte);
    result.set(captchaByte, actionCodeByte.length);
    result.set(dataBuff, actionCodeByte.length + captchaByte.length);

    this.ws.send(result);
  }
}

exports.default = BotController;
