System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (r, t) {
    var e;
    return {
      setters: [
        function (r) {
          e = r.default;
        },
      ],
      execute: function () {
        var n = r("__cjsMetaURL", t.meta.url);
        e.define(
          n,
          function (r, t, e, n, o) {
            var a = r;
            (a.length = function (r) {
              for (var t = 0, e = 0, n = 0; n < r.length; ++n)
                (e = r.charCodeAt(n)) < 128
                  ? (t += 1)
                  : e < 2048
                  ? (t += 2)
                  : 55296 == (64512 & e) &&
                    56320 == (64512 & r.charCodeAt(n + 1))
                  ? (++n, (t += 4))
                  : (t += 3);
              return t;
            }),
              (a.read = function (r, t, e) {
                if (e - t < 1) return "";
                for (var n, o = null, a = [], i = 0; t < e; )
                  (n = r[t++]) < 128
                    ? (a[i++] = n)
                    : n > 191 && n < 224
                    ? (a[i++] = ((31 & n) << 6) | (63 & r[t++]))
                    : n > 239 && n < 365
                    ? ((n =
                        (((7 & n) << 18) |
                          ((63 & r[t++]) << 12) |
                          ((63 & r[t++]) << 6) |
                          (63 & r[t++])) -
                        65536),
                      (a[i++] = 55296 + (n >> 10)),
                      (a[i++] = 56320 + (1023 & n)))
                    : (a[i++] =
                        ((15 & n) << 12) |
                        ((63 & r[t++]) << 6) |
                        (63 & r[t++])),
                    i > 8191 &&
                      ((o || (o = [])).push(
                        String.fromCharCode.apply(String, a)
                      ),
                      (i = 0));
                return o
                  ? (i &&
                      o.push(String.fromCharCode.apply(String, a.slice(0, i))),
                    o.join(""))
                  : String.fromCharCode.apply(String, a.slice(0, i));
              }),
              (a.write = function (r, t, e) {
                for (var n, o, a = e, i = 0; i < r.length; ++i)
                  (n = r.charCodeAt(i)) < 128
                    ? (t[e++] = n)
                    : n < 2048
                    ? ((t[e++] = (n >> 6) | 192), (t[e++] = (63 & n) | 128))
                    : 55296 == (64512 & n) &&
                      56320 == (64512 & (o = r.charCodeAt(i + 1)))
                    ? ((n = 65536 + ((1023 & n) << 10) + (1023 & o)),
                      ++i,
                      (t[e++] = (n >> 18) | 240),
                      (t[e++] = ((n >> 12) & 63) | 128),
                      (t[e++] = ((n >> 6) & 63) | 128),
                      (t[e++] = (63 & n) | 128))
                    : ((t[e++] = (n >> 12) | 224),
                      (t[e++] = ((n >> 6) & 63) | 128),
                      (t[e++] = (63 & n) | 128));
                return e - a;
              }),
              e.exports;
          },
          {}
        );
      },
    };
  }
);
