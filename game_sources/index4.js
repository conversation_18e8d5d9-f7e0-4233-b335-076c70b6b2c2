System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (e, t) {
    var n;
    return {
      setters: [
        function (e) {
          n = e.default;
        },
      ],
      execute: function () {
        var r = e("__cjsMetaURL", t.meta.url);
        n.define(
          r,
          function (e, t, n, r, o) {
            function a(e) {
              return (
                "undefined" != typeof Float32Array
                  ? (function () {
                      var t = new Float32Array([-0]),
                        n = new Uint8Array(t.buffer),
                        r = 128 === n[3];
                      function o(e, r, o) {
                        (t[0] = e),
                          (r[o] = n[0]),
                          (r[o + 1] = n[1]),
                          (r[o + 2] = n[2]),
                          (r[o + 3] = n[3]);
                      }
                      function a(e, r, o) {
                        (t[0] = e),
                          (r[o] = n[3]),
                          (r[o + 1] = n[2]),
                          (r[o + 2] = n[1]),
                          (r[o + 3] = n[0]);
                      }
                      function u(e, r) {
                        return (
                          (n[0] = e[r]),
                          (n[1] = e[r + 1]),
                          (n[2] = e[r + 2]),
                          (n[3] = e[r + 3]),
                          t[0]
                        );
                      }
                      function i(e, r) {
                        return (
                          (n[3] = e[r]),
                          (n[2] = e[r + 1]),
                          (n[1] = e[r + 2]),
                          (n[0] = e[r + 3]),
                          t[0]
                        );
                      }
                      (e.writeFloatLE = r ? o : a),
                        (e.writeFloatBE = r ? a : o),
                        (e.readFloatLE = r ? u : i),
                        (e.readFloatBE = r ? i : u);
                    })()
                  : (function () {
                      function t(e, t, n, r) {
                        var o = t < 0 ? 1 : 0;
                        if ((o && (t = -t), 0 === t))
                          e(1 / t > 0 ? 0 : 2147483648, n, r);
                        else if (isNaN(t)) e(2143289344, n, r);
                        else if (t > 34028234663852886e22)
                          e(((o << 31) | 2139095040) >>> 0, n, r);
                        else if (t < 11754943508222875e-54)
                          e(
                            ((o << 31) |
                              Math.round(t / 1401298464324817e-60)) >>>
                              0,
                            n,
                            r
                          );
                        else {
                          var a = Math.floor(Math.log(t) / Math.LN2);
                          e(
                            ((o << 31) |
                              ((a + 127) << 23) |
                              (8388607 &
                                Math.round(t * Math.pow(2, -a) * 8388608))) >>>
                              0,
                            n,
                            r
                          );
                        }
                      }
                      function n(e, t, n) {
                        var r = e(t, n),
                          o = 2 * (r >> 31) + 1,
                          a = (r >>> 23) & 255,
                          u = 8388607 & r;
                        return 255 === a
                          ? u
                            ? NaN
                            : o * (1 / 0)
                          : 0 === a
                          ? 1401298464324817e-60 * o * u
                          : o * Math.pow(2, a - 150) * (u + 8388608);
                      }
                      (e.writeFloatLE = t.bind(null, u)),
                        (e.writeFloatBE = t.bind(null, i)),
                        (e.readFloatLE = n.bind(null, l)),
                        (e.readFloatBE = n.bind(null, f));
                    })(),
                "undefined" != typeof Float64Array
                  ? (function () {
                      var t = new Float64Array([-0]),
                        n = new Uint8Array(t.buffer),
                        r = 128 === n[7];
                      function o(e, r, o) {
                        (t[0] = e),
                          (r[o] = n[0]),
                          (r[o + 1] = n[1]),
                          (r[o + 2] = n[2]),
                          (r[o + 3] = n[3]),
                          (r[o + 4] = n[4]),
                          (r[o + 5] = n[5]),
                          (r[o + 6] = n[6]),
                          (r[o + 7] = n[7]);
                      }
                      function a(e, r, o) {
                        (t[0] = e),
                          (r[o] = n[7]),
                          (r[o + 1] = n[6]),
                          (r[o + 2] = n[5]),
                          (r[o + 3] = n[4]),
                          (r[o + 4] = n[3]),
                          (r[o + 5] = n[2]),
                          (r[o + 6] = n[1]),
                          (r[o + 7] = n[0]);
                      }
                      function u(e, r) {
                        return (
                          (n[0] = e[r]),
                          (n[1] = e[r + 1]),
                          (n[2] = e[r + 2]),
                          (n[3] = e[r + 3]),
                          (n[4] = e[r + 4]),
                          (n[5] = e[r + 5]),
                          (n[6] = e[r + 6]),
                          (n[7] = e[r + 7]),
                          t[0]
                        );
                      }
                      function i(e, r) {
                        return (
                          (n[7] = e[r]),
                          (n[6] = e[r + 1]),
                          (n[5] = e[r + 2]),
                          (n[4] = e[r + 3]),
                          (n[3] = e[r + 4]),
                          (n[2] = e[r + 5]),
                          (n[1] = e[r + 6]),
                          (n[0] = e[r + 7]),
                          t[0]
                        );
                      }
                      (e.writeDoubleLE = r ? o : a),
                        (e.writeDoubleBE = r ? a : o),
                        (e.readDoubleLE = r ? u : i),
                        (e.readDoubleBE = r ? i : u);
                    })()
                  : (function () {
                      function t(e, t, n, r, o, a) {
                        var u = r < 0 ? 1 : 0;
                        if ((u && (r = -r), 0 === r))
                          e(0, o, a + t),
                            e(1 / r > 0 ? 0 : 2147483648, o, a + n);
                        else if (isNaN(r))
                          e(0, o, a + t), e(2146959360, o, a + n);
                        else if (r > 17976931348623157e292)
                          e(0, o, a + t),
                            e(((u << 31) | 2146435072) >>> 0, o, a + n);
                        else {
                          var i;
                          if (r < 22250738585072014e-324)
                            e((i = r / 5e-324) >>> 0, o, a + t),
                              e(((u << 31) | (i / 4294967296)) >>> 0, o, a + n);
                          else {
                            var l = Math.floor(Math.log(r) / Math.LN2);
                            1024 === l && (l = 1023),
                              e(
                                (4503599627370496 *
                                  (i = r * Math.pow(2, -l))) >>>
                                  0,
                                o,
                                a + t
                              ),
                              e(
                                ((u << 31) |
                                  ((l + 1023) << 20) |
                                  ((1048576 * i) & 1048575)) >>>
                                  0,
                                o,
                                a + n
                              );
                          }
                        }
                      }
                      function n(e, t, n, r, o) {
                        var a = e(r, o + t),
                          u = e(r, o + n),
                          i = 2 * (u >> 31) + 1,
                          l = (u >>> 20) & 2047,
                          f = 4294967296 * (1048575 & u) + a;
                        return 2047 === l
                          ? f
                            ? NaN
                            : i * (1 / 0)
                          : 0 === l
                          ? 5e-324 * i * f
                          : i * Math.pow(2, l - 1075) * (f + 4503599627370496);
                      }
                      (e.writeDoubleLE = t.bind(null, u, 0, 4)),
                        (e.writeDoubleBE = t.bind(null, i, 4, 0)),
                        (e.readDoubleLE = n.bind(null, l, 0, 4)),
                        (e.readDoubleBE = n.bind(null, f, 4, 0));
                    })(),
                e
              );
            }
            function u(e, t, n) {
              (t[n] = 255 & e),
                (t[n + 1] = (e >>> 8) & 255),
                (t[n + 2] = (e >>> 16) & 255),
                (t[n + 3] = e >>> 24);
            }
            function i(e, t, n) {
              (t[n] = e >>> 24),
                (t[n + 1] = (e >>> 16) & 255),
                (t[n + 2] = (e >>> 8) & 255),
                (t[n + 3] = 255 & e);
            }
            function l(e, t) {
              return (
                (e[t] |
                  (e[t + 1] << 8) |
                  (e[t + 2] << 16) |
                  (e[t + 3] << 24)) >>>
                0
              );
            }
            function f(e, t) {
              return (
                ((e[t] << 24) |
                  (e[t + 1] << 16) |
                  (e[t + 2] << 8) |
                  e[t + 3]) >>>
                0
              );
            }
            (n.exports = a(a)),
              n.exports,
              n.exports.writeFloatLE,
              n.exports.writeFloatBE,
              n.exports.readFloatLE,
              n.exports.readFloatBE,
              n.exports.writeDoubleLE,
              n.exports.writeDoubleBE,
              n.exports.readDoubleLE,
              n.exports.readDoubleBE;
          },
          {}
        );
      },
    };
  }
);
