System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/index-minimal.js",
  ],
  function (n, e) {
    var i, t;
    return {
      setters: [
        function (n) {
          i = n.default;
        },
        function (n) {
          t = n.__cjsMetaURL;
        },
      ],
      execute: function () {
        var r = n("__cjsMetaURL", e.meta.url);
        i.define(
          r,
          function (n, e, i, t, r) {
            (i.exports = e("./src/index-minimal")), i.exports;
          },
          function () {
            return {
              "./src/index-minimal": t,
            };
          }
        );
      },
    };
  }
);
