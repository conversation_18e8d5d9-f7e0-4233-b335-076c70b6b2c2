System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/writer.js",
    process.cwd() + "/game_sources/writer_buffer.js",
    process.cwd() + "/game_sources/reader.js",
    process.cwd() + "/game_sources/reader_buffer.js",
    process.cwd() + "/game_sources/minimal2.js",
    process.cwd() + "/game_sources/rpc.js",
    process.cwd() + "/game_sources/roots.js",
  ],
  function (r, e) {
    var t, i, n, f, u, c, a, s;
    return {
      setters: [
        function (r) {
          t = r.default;
        },
        function (r) {
          i = r.__cjsMetaURL;
        },
        function (r) {
          n = r.__cjsMetaURL;
        },
        function (r) {
          f = r.__cjsMetaURL;
        },
        function (r) {
          u = r.__cjsMetaURL;
        },
        function (r) {
          c = r.__cjsMetaURL;
        },
        function (r) {
          a = r.__cjsMetaURL;
        },
        function (r) {
          s = r.__cjsMetaURL;
        },
      ],
      execute: function () {
        var o = r("__cjsMetaURL", e.meta.url);
        t.define(
          o,
          function (r, e, t, i, n) {
            var f = r;
            function u() {
              f.util._configure(),
                f.Writer._configure(f.BufferWriter),
                f.Reader._configure(f.BufferReader);
            }
            (f.build = "minimal"),
              (f.Writer = e("./writer")),
              (f.BufferWriter = e("./writer_buffer")),
              (f.Reader = e("./reader")),
              (f.BufferReader = e("./reader_buffer")),
              (f.util = e("./util/minimal")),
              (f.rpc = e("./rpc")),
              (f.roots = e("./roots")),
              (f.configure = u),
              u(),
              t.exports;
          },
          function () {
            return {
              "./writer": i,
              "./writer_buffer": n,
              "./reader": f,
              "./reader_buffer": u,
              "./util/minimal": c,
              "./rpc": a,
              "./roots": s,
            };
          }
        );
      },
    };
  }
);
