System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/minimal2.js",
  ],
  function (t, i) {
    var s, r;
    return {
      setters: [
        function (t) {
          s = t.default;
        },
        function (t) {
          r = t.__cjsMetaURL;
        },
      ],
      execute: function () {
        var o = t("__cjsMetaURL", i.meta.url);
        s.define(
          o,
          function (t, i, s, r, o) {
            s.exports = p;
            var n,
              e = i("./util/minimal"),
              h = e.LongBits,
              u = e.utf8;
            function f(t, i) {
              return RangeError(
                "index out of range: " +
                  t.pos +
                  " + " +
                  (i || 1) +
                  " > " +
                  t.len
              );
            }
            function p(t) {
              (this.buf = t), (this.pos = 0), (this.len = t.length);
            }
            var l,
              a =
                "undefined" != typeof Uint8Array
                  ? function (t) {
                      if (t instanceof Uint8Array || Array.isArray(t))
                        return new p(t);
                      throw Error("illegal buffer");
                    }
                  : function (t) {
                      if (Array.isArray(t)) return new p(t);
                      throw Error("illegal buffer");
                    },
              c = function () {
                return e.Buffer
                  ? function (t) {
                      return (p.create = function (t) {
                        return e.Buffer.isBuffer(t) ? new n(t) : a(t);
                      })(t);
                    }
                  : a;
              };
            function b() {
              var t = new h(0, 0),
                i = 0;
              if (!(this.len - this.pos > 4)) {
                for (; i < 3; ++i) {
                  if (this.pos >= this.len) throw f(this);
                  if (
                    ((t.lo =
                      (t.lo | ((127 & this.buf[this.pos]) << (7 * i))) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return t;
                }
                return (
                  (t.lo =
                    (t.lo | ((127 & this.buf[this.pos++]) << (7 * i))) >>> 0),
                  t
                );
              }
              for (; i < 4; ++i)
                if (
                  ((t.lo =
                    (t.lo | ((127 & this.buf[this.pos]) << (7 * i))) >>> 0),
                  this.buf[this.pos++] < 128)
                )
                  return t;
              if (
                ((t.lo = (t.lo | ((127 & this.buf[this.pos]) << 28)) >>> 0),
                (t.hi = (t.hi | ((127 & this.buf[this.pos]) >> 4)) >>> 0),
                this.buf[this.pos++] < 128)
              )
                return t;
              if (((i = 0), this.len - this.pos > 4)) {
                for (; i < 5; ++i)
                  if (
                    ((t.hi =
                      (t.hi | ((127 & this.buf[this.pos]) << (7 * i + 3))) >>>
                      0),
                    this.buf[this.pos++] < 128)
                  )
                    return t;
              } else
                for (; i < 5; ++i) {
                  if (this.pos >= this.len) throw f(this);
                  if (
                    ((t.hi =
                      (t.hi | ((127 & this.buf[this.pos]) << (7 * i + 3))) >>>
                      0),
                    this.buf[this.pos++] < 128)
                  )
                    return t;
                }
              throw Error("invalid varint encoding");
            }
            function y(t, i) {
              return (
                (t[i - 4] |
                  (t[i - 3] << 8) |
                  (t[i - 2] << 16) |
                  (t[i - 1] << 24)) >>>
                0
              );
            }
            function w() {
              if (this.pos + 8 > this.len) throw f(this, 8);
              return new h(
                y(this.buf, (this.pos += 4)),
                y(this.buf, (this.pos += 4))
              );
            }
            (p.create = c()),
              (p.prototype._slice =
                e.Array.prototype.subarray || e.Array.prototype.slice),
              (p.prototype.uint32 =
                ((l = 4294967295),
                function () {
                  if (
                    ((l = (127 & this.buf[this.pos]) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return l;
                  if (
                    ((l = (l | ((127 & this.buf[this.pos]) << 7)) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return l;
                  if (
                    ((l = (l | ((127 & this.buf[this.pos]) << 14)) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return l;
                  if (
                    ((l = (l | ((127 & this.buf[this.pos]) << 21)) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return l;
                  if (
                    ((l = (l | ((15 & this.buf[this.pos]) << 28)) >>> 0),
                    this.buf[this.pos++] < 128)
                  )
                    return l;
                  if ((this.pos += 5) > this.len)
                    throw ((this.pos = this.len), f(this, 10));
                  return l;
                })),
              (p.prototype.int32 = function () {
                return 0 | this.uint32();
              }),
              (p.prototype.sint32 = function () {
                var t = this.uint32();
                return ((t >>> 1) ^ -(1 & t)) | 0;
              }),
              (p.prototype.bool = function () {
                return 0 !== this.uint32();
              }),
              (p.prototype.fixed32 = function () {
                if (this.pos + 4 > this.len) throw f(this, 4);
                return y(this.buf, (this.pos += 4));
              }),
              (p.prototype.sfixed32 = function () {
                if (this.pos + 4 > this.len) throw f(this, 4);
                return 0 | y(this.buf, (this.pos += 4));
              }),
              (p.prototype.float = function () {
                if (this.pos + 4 > this.len) throw f(this, 4);
                var t = e.float.readFloatLE(this.buf, this.pos);
                return (this.pos += 4), t;
              }),
              (p.prototype.double = function () {
                if (this.pos + 8 > this.len) throw f(this, 4);
                var t = e.float.readDoubleLE(this.buf, this.pos);
                return (this.pos += 8), t;
              }),
              (p.prototype.bytes = function () {
                var t = this.uint32(),
                  i = this.pos,
                  s = this.pos + t;
                if (s > this.len) throw f(this, t);
                if (((this.pos += t), Array.isArray(this.buf)))
                  return this.buf.slice(i, s);
                if (i === s) {
                  var r = e.Buffer;
                  return r ? r.alloc(0) : new this.buf.constructor(0);
                }
                return this._slice.call(this.buf, i, s);
              }),
              (p.prototype.string = function () {
                var t = this.bytes();
                return u.read(t, 0, t.length);
              }),
              (p.prototype.skip = function (t) {
                if ("number" == typeof t) {
                  if (this.pos + t > this.len) throw f(this, t);
                  this.pos += t;
                } else
                  do {
                    if (this.pos >= this.len) throw f(this);
                  } while (128 & this.buf[this.pos++]);
                return this;
              }),
              (p.prototype.skipType = function (t) {
                switch (t) {
                  case 0:
                    this.skip();
                    break;
                  case 1:
                    this.skip(8);
                    break;
                  case 2:
                    this.skip(this.uint32());
                    break;
                  case 3:
                    for (; 4 != (t = 7 & this.uint32()); ) this.skipType(t);
                    break;
                  case 5:
                    this.skip(4);
                    break;
                  default:
                    throw Error(
                      "invalid wire type " + t + " at offset " + this.pos
                    );
                }
                return this;
              }),
              (p._configure = function (t) {
                (n = t), (p.create = c()), n._configure();
                var i = e.Long ? "toLong" : "toNumber";
                e.merge(p.prototype, {
                  int64: function () {
                    return b.call(this)[i](!1);
                  },
                  uint64: function () {
                    return b.call(this)[i](!0);
                  },
                  sint64: function () {
                    return b.call(this).zzDecode()[i](!1);
                  },
                  fixed64: function () {
                    return w.call(this)[i](!0);
                  },
                  sfixed64: function () {
                    return w.call(this)[i](!1);
                  },
                });
              }),
              s.exports;
          },
          function () {
            return {
              "./util/minimal": r,
            };
          }
        );
      },
    };
  }
);
