System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (e, t) {
    var r;
    return {
      setters: [
        function (e) {
          r = e.default;
        },
      ],
      execute: function () {
        var s = e("__cjsMetaURL", t.meta.url);
        r.define(
          s,
          function (e, t, r, s, n) {
            (r.exports = {}), r.exports;
          },
          {}
        );
      },
    };
  }
);
