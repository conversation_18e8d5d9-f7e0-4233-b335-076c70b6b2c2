System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/writer.js",
    process.cwd() + "/game_sources/minimal2.js",
  ],
  function (t, e) {
    var r, n, i;
    return {
      setters: [
        function (t) {
          r = t.default;
        },
        function (t) {
          n = t.__cjsMetaURL;
        },
        function (t) {
          i = t.__cjsMetaURL;
        },
      ],
      execute: function () {
        var f = t("__cjsMetaURL", e.meta.url);
        r.define(
          f,
          function (t, e, r, n, i) {
            r.exports = o;
            var f = e("./writer");
            (o.prototype = Object.create(f.prototype)).constructor = o;
            var u = e("./util/minimal");
            function o() {
              f.call(this);
            }
            function s(t, e, r) {
              t.length < 40
                ? u.utf8.write(t, e, r)
                : e.utf8Write
                ? e.utf8Write(t, r)
                : e.write(t, r);
            }
            (o._configure = function () {
              (o.alloc = u._Buffer_allocUnsafe),
                (o.writeBytesBuffer =
                  u.Buffer &&
                  u.Buffer.prototype instanceof Uint8Array &&
                  "set" === u.Buffer.prototype.set.name
                    ? function (t, e, r) {
                        e.set(t, r);
                      }
                    : function (t, e, r) {
                        if (t.copy) t.copy(e, r, 0, t.length);
                        else for (var n = 0; n < t.length; ) e[r++] = t[n++];
                      });
            }),
              (o.prototype.bytes = function (t) {
                u.isString(t) && (t = u._Buffer_from(t, "base64"));
                var e = t.length >>> 0;
                return (
                  this.uint32(e),
                  e && this._push(o.writeBytesBuffer, e, t),
                  this
                );
              }),
              (o.prototype.string = function (t) {
                var e = u.Buffer.byteLength(t);
                return this.uint32(e), e && this._push(s, e, t), this;
              }),
              o._configure(),
              r.exports;
          },
          function () {
            return {
              "./writer": n,
              "./util/minimal": i,
            };
          }
        );
      },
    };
  }
);
