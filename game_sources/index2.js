System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (r, e) {
    var t;
    return {
      setters: [
        function (r) {
          t = r.default;
        },
      ],
      execute: function () {
        var a = r("__cjsMetaURL", e.meta.url);
        t.define(
          a,
          function (r, e, t, a, n) {
            var i = r;
            i.length = function (r) {
              var e = r.length;
              if (!e) return 0;
              for (var t = 0; --e % 4 > 1 && "=" === r.charAt(e); ) ++t;
              return Math.ceil(3 * r.length) / 4 - t;
            };
            for (var o = new Array(64), c = new Array(123), s = 0; s < 64; )
              c[
                (o[s] =
                  s < 26
                    ? s + 65
                    : s < 52
                    ? s + 71
                    : s < 62
                    ? s - 4
                    : (s - 59) | 43)
              ] = s++;
            i.encode = function (r, e, t) {
              for (var a, n = null, i = [], c = 0, s = 0; e < t; ) {
                var u = r[e++];
                switch (s) {
                  case 0:
                    (i[c++] = o[u >> 2]), (a = (3 & u) << 4), (s = 1);
                    break;
                  case 1:
                    (i[c++] = o[a | (u >> 4)]), (a = (15 & u) << 2), (s = 2);
                    break;
                  case 2:
                    (i[c++] = o[a | (u >> 6)]), (i[c++] = o[63 & u]), (s = 0);
                }
                c > 8191 &&
                  ((n || (n = [])).push(String.fromCharCode.apply(String, i)),
                  (c = 0));
              }
              return (
                s && ((i[c++] = o[a]), (i[c++] = 61), 1 === s && (i[c++] = 61)),
                n
                  ? (c &&
                      n.push(String.fromCharCode.apply(String, i.slice(0, c))),
                    n.join(""))
                  : String.fromCharCode.apply(String, i.slice(0, c))
              );
            };
            var u = "invalid encoding";
            (i.decode = function (r, e, t) {
              for (var a, n = t, i = 0, o = 0; o < r.length; ) {
                var s = r.charCodeAt(o++);
                if (61 === s && i > 1) break;
                if (void 0 === (s = c[s])) throw Error(u);
                switch (i) {
                  case 0:
                    (a = s), (i = 1);
                    break;
                  case 1:
                    (e[t++] = (a << 2) | ((48 & s) >> 4)), (a = s), (i = 2);
                    break;
                  case 2:
                    (e[t++] = ((15 & a) << 4) | ((60 & s) >> 2)),
                      (a = s),
                      (i = 3);
                    break;
                  case 3:
                    (e[t++] = ((3 & a) << 6) | s), (i = 0);
                }
              }
              if (1 === i) throw Error(u);
              return t - n;
            }),
              (i.test = function (r) {
                return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(
                  r
                );
              }),
              t.exports;
          },
          {}
        );
      },
    };
  }
);
