System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (e, t) {
    var n;
    return {
      setters: [
        function (e) {
          n = e.default;
        },
      ],
      execute: function () {
        var r = e("__cjsMetaURL", t.meta.url);
        n.define(
          r,
          function (e, t, n, r, u) {
            (n.exports = function (e, t, n) {
              var r = n || 8192,
                u = r >>> 1,
                c = null,
                i = r;
              return function (n) {
                if (n < 1 || n > u) return e(n);
                i + n > r && ((c = e(r)), (i = 0));
                var s = t.call(c, i, (i += n));
                return 7 & i && (i = 1 + (7 | i)), s;
              };
            }),
              n.exports;
          },
          {}
        );
      },
    };
  }
);
