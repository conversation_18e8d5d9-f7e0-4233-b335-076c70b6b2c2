System.register(
  "chunks:///_virtual/MonsterObj.ts",
  [
    "./rollupPluginModLoBabelHelpers.js",
    "cc",
    "./AtkStrategy.ts",
    "./ChaseAS.ts",
    "./CreatureObj.ts",
    "./BattleDef.ts",
    "./BattleMgr.ts",
    "./EffectType.ts",
    "./SkillAS.ts",
    "./ChargeAS.ts",
    "./SummonAS.ts",
    "./ObjMgr.ts",
    "./proto.mjs_cjs=&original=.js",
    "./UIRoot.ts",
    "./BossAS.ts",
    "./proto.js",
  ],
  function (t) {
    var e, n, i, s, a, g, r, o, h, u, f, c, A, d, p, l, k, S, m;
    return {
      setters: [
        function (t) {
          e = t.inheritsLoose;
        },
        function (t) {
          n = t.cclegacy;
        },
        function (t) {
          (i = t.StrategyType), (s = t.AtkStrategy);
        },
        function (t) {
          a = t.ChaseAtkStrategy;
        },
        function (t) {
          g = t.default;
        },
        function (t) {
          (r = t.CampType),
            (o = t.ObjType),
            (h = t.AtkObjType),
            (u = t.BattleDef);
        },
        function (t) {
          f = t.BattleMgr;
        },
        function (t) {
          c = t.EffectType;
        },
        function (t) {
          A = t.SkillAtkStrategy;
        },
        function (t) {
          d = t.ChargeAtkStrategy;
        },
        function (t) {
          p = t.SummonAtkStrategy;
        },
        function (t) {
          l = t.default;
        },
        null,
        function (t) {
          k = t.default;
        },
        function (t) {
          S = t.BossAtkStrategy;
        },
        function (t) {
          m = t.default;
        },
      ],
      execute: function () {
        n._RF.push({}, "90a06PsA5lOKYTP1Q9n1Vc7", "MonsterObj", void 0);
        t(
          "default",
          (function (t) {
            function n() {
              for (
                var e, n = arguments.length, i = new Array(n), s = 0;
                s < n;
                s++
              )
                i[s] = arguments[s];
              return (
                ((e = t.call.apply(t, [this].concat(i)) || this).aosn = 0),
                (e.atkStrategy = void 0),
                (e.beAtkUnique = new Set()),
                (e.Charging = !1),
                (e.append = 0),
                (e.atkTick = 0),
                e
              );
            }
            e(n, t);
            var g = n.prototype;
            return (
              (g.getAtkStratege = function () {
                return this.atkStrategy;
              }),
              (g.init = function (e, n, i, s) {
                t.prototype.init.call(this, e, n, i, s),
                  (this.cType = r.Dungeon);
                var a = DataCenter.Monster.Get(n);
                (this.cfg = a),
                  (this.oType = o.Monster),
                  this.getAttr().init(s.attrs),
                  (this.append = s.append),
                  (this.aosn = l.ins.generateAtkObjSN()),
                  2 == this.cfg.Type &&
                    (k.fight.showBossPanel(!0, a.Name),
                    k.fight.setBossHP(this.getAttr().getHpPercent()));
              }),
              (g.destroy = function () {
                t.prototype.destroy.call(this),
                  this.beAtkUnique.clear(),
                  (this.beAtkUnique = null),
                  (this.cfg = null),
                  this.atkStrategy.destory();
              }),
              (g.bornBuff = function () {
                this.cfg.BornBuff > 0 &&
                  this.getAttr().addBuff(
                    this.getAttrVal(m.msg.AttrType.AT_Dmg),
                    this.cfg.BornBuff,
                    this.getAOType(),
                    this.getAOSN(),
                    this.getSN()
                  );
              }),
              (g.afterInit = function () {
                this.onAttrChanged(), this.startAI();
              }),
              (g.startAI = function () {
                var t = this.cfg.AI;
                t == i.Null
                  ? (this.atkStrategy = new s())
                  : t == i.Chase
                  ? (this.atkStrategy = new a())
                  : t == i.Charge
                  ? (this.atkStrategy = new d())
                  : t == i.Skill
                  ? (this.atkStrategy = new A())
                  : t == i.Summon
                  ? (this.atkStrategy = new p())
                  : t == i.Boss && (this.atkStrategy = new S()),
                  null == this.atkStrategy && console.log("unknow aitype"),
                  this.atkStrategy.init(this, this.cfg.AIVal);
              }),
              (g.addBuff = function (t, e, n, i, s) {
                var a = this.getAttr().getDebuffFlag();
                this.getAttr().addBuff(t, e, n, i, s),
                  this.onAttrChanged(),
                  this.onBuffChanged(a);
              }),
              (g.update = function () {
                if (
                  !this.getAttr().isDead() &&
                  (this.atkStrategy.onUpdate(), this.getAttr().getBuffLen() > 0)
                ) {
                  var t = this.getAttr().getDebuffFlag();
                  this.getAttr().onUpdate() &&
                    (this.onAttrChanged(), this.onBuffChanged(t));
                }
              }),
              (g.getName = function () {
                return this.cfg.Name;
              }),
              (g.updateViewAttr = function () {
                this.node.setMoveSpeed(this.getAttr().getMoveSpeed()),
                  this.node.setAttackDuration(
                    this.getAttrVal(m.msg.AttrType.AT_AtkLength)
                  );
              }),
              (g.setViewNode = function (e) {
                t.prototype.setViewNode.call(this, e), this.updateViewAttr();
              }),
              (g.isDead = function () {
                return !this.cfg;
              }),
              (g.getDmg = function () {
                return this.getAttrVal(m.msg.AttrType.AT_Dmg);
              }),
              (g.getDmgCritical = function () {
                return this.getAttrVal(m.msg.AttrType.AT_Dmg);
              }),
              (g.getAOType = function () {
                return h.Monster;
              }),
              (g.getAOID = function () {
                return this.cfgID;
              }),
              (g.getKnockBack = function () {
                return 0;
              }),
              (g.getTargetMax = function () {
                return 1;
              }),
              (g.getOwnerSN = function () {
                return this.getSN();
              }),
              (g.getAOSN = function () {
                return this.aosn;
              }),
              (g.getWeaponID = function () {
                return 0;
              }),
              (g.setAtkTick = function (t) {
                this.atkTick = t;
              }),
              (g.getLastAtkTick = function () {
                return this.atkTick;
              }),
              (g.getAtkTime = function () {
                return this.getAttrVal(m.msg.AttrType.AT_AtkCD);
              }),
              (g.onAttrChanged = function () {
                this.node && this.updateViewAttr();
              }),
              (g.onBuffChanged = function (t) {
                t == this.getAttr().getDebuffFlag() &&
                  this.node &&
                  (this.getAttr().canMove()
                    ? this.node.setMoveLimit(!1)
                    : this.node.setMoveLimit(!0),
                  this.getAttr().isStun()
                    ? this.node.showStun(!0)
                    : this.node.showStun(!1),
                  this.getAttr().isBurning()
                    ? this.node.showBurning(!0)
                    : this.node.showBurning(!1));
              }),
              (g.beAtk = function (t, e) {
                if (
                  t &&
                  this.node &&
                  this.getAttr() &&
                  !this.isDead() &&
                  !t.isAtkLimit()
                ) {
                  var n = t.getAtkUnique();
                  if (!this.beAtkUnique.has(n)) {
                    this.beAtkUnique.add(n), t.atkOccur();
                    var i,
                      s = !1,
                      a = u.UnitePct,
                      g = 0,
                      r = t.getAppendBuff(),
                      o = this.getPosX(),
                      A = this.getPosY(),
                      d = this.getSN(),
                      p = t.getAOType();
                    this.getAttr().onBeAtk(), (g = t.getDmg());
                    var S = l.ins.getChar();
                    if (p == h.Weapon) {
                      var y = l.ins
                        .getChar()
                        .getAttrVal(m.msg.AttrType.AT_CriticalRate);
                      y > 0 &&
                        (a = f.ins.randNextEx(0, u.UnitePct - 1)) < y &&
                        (s = !0),
                        s && (g = t.getDmgCritical());
                    }
                    var T = {
                      et: c.Nothing,
                    };
                    (T.et = c.BeDmg), (T.dmg = g), (T.critical = s);
                    var D = this.getAttr().onDamage(g);
                    if (
                      ((i =
                        t.getKnockback() -
                        this.getAttrVal(m.msg.AttrType.AT_KnockbackReduce)) >
                        0 &&
                        !this.Charging &&
                        ((T.knockBack = i),
                        (T.knockBackTime = f.ins.knockBackTime)),
                      D
                        ? ((T.et = c.BeDmgToDie),
                          (T.sn = this.sn),
                          2 == this.cfg.Type &&
                            k.fight.setBossHP(this.getAttr().getHpPercent()))
                        : (2 == this.cfg.Type &&
                            k.fight.setBossHP(this.getAttr().getHpPercent()),
                          r > 0 &&
                            (p == h.Tower
                              ? this.addBuff(
                                  r,
                                  t.getDmg(),
                                  p,
                                  t.getAOSN(),
                                  t.getOwnerCrtSN()
                                )
                              : p == h.Weapon &&
                                this.addBuff(
                                  r,
                                  t.getDmg(),
                                  p,
                                  t.getAOSN(),
                                  t.getWeaponSN()
                                ))),
                      p == h.Weapon)
                    )
                      this.node.onDamaged(S.node, T),
                        t.getEquipment().addStageDmg(g);
                    else if (p == h.Tower) {
                      var B = l.ins.getTower(t.getOwnerCrtSN());
                      null != B && B.node
                        ? this.node.onDamaged(B.node, T)
                        : this.node.onDamaged(S.node, T),
                        B.getSrcWeapon() && B.getSrcWeapon().addStageDmg(g);
                    } else p == h.Talent && this.node.onDamaged(S.node, T);
                    D && f.ins.getStage().onKillMonster(this);
                    var C = new m.msg.BattleDmg();
                    if (
                      ((C.CrtSN = d),
                      C.Pos.push(o),
                      C.Pos.push(A),
                      (C.Critical = s),
                      (C.CriticalRand = a),
                      (C.Dmg = g),
                      (C.Knockback = i),
                      r > 0)
                    ) {
                      var w = new m.msg.BattleBuff();
                      (w.ID = r), (C.AppendBuff = w);
                    }
                    var v = f.ins.findAtkMsg(t.getBatch(), t.getSegament());
                    if (null != v) v.CltDmg.push(C);
                    else {
                      var N = new m.msg.GameCharAtk();
                      (N.AOT = Number(p)),
                        N.AOInfo.push(t.getBatch(), t.getSegament()),
                        p == h.Weapon
                          ? N.AOInfo.push(t.getWeaponSN())
                          : p == h.Tower && N.AOInfo.push(t.getOwnerCrtSN()),
                        N.AOInfo.push(t.getAtkIntl()),
                        N.CltDmg.push(C),
                        f.ins.WeaponAtkMsgs.push(N);
                    }
                  }
                }
              }),
              (g.onBuffDmg = function (t, e, n, i, s) {
                if (null != this.node) {
                  var a = {
                    et: c.Nothing,
                  };
                  (a.et = c.DotDmg), (a.dmg = t), (a.critical = !1);
                  var g = this.getAttr().onDamage(t);
                  g && ((a.die = g), f.ins.getStage().onKillMonster(this));
                  var r = l.ins.getChar();
                  if ((this.node.onDamaged(r, a), n == h.Weapon)) {
                    var o = l.ins.getChar().getWeapon(s);
                    o && o.addStageDmg(t);
                  } else if (n == h.Tower) {
                    var u = l.ins.getTower(s);
                    u && u.getSrcWeapon() && u.getSrcWeapon().addStageDmg(t);
                  }
                  var A = new m.msg.BattleDmgEx();
                  (A.Type = 0),
                    (A.TypeVals = [e, n, s]),
                    (A.MSN = this.getSN()),
                    (A.Pos = [this.getPosX(), this.getPosY()]),
                    f.ins.ExAtkMsgs.push(A);
                }
              }),
              (g.getAppendBuff = function () {
                return this.append;
              }),
              n
            );
          })(g)
        );
        n._RF.pop();
      },
    };
  }
);
