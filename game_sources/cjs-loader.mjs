System.register([], function (e) {
  return {
    execute: function () {
      e(
        "default",
        new ((function () {
          function e() {
            (this._registry = {}), (this._moduleCache = {});
          }
          var r = e.prototype;
          return (
            (r.define = function (e, r, t) {
              this._registry[e] = {
                factory: r,
                resolveMap: t,
              };
            }),
            (r.require = function (e) {
              return this._require(e);
            }),
            (r.throwInvalidWrapper = function (e, r) {
              throw new Error(
                "Module '" +
                  e +
                  "' imported from '" +
                  r +
                  "' is expected be an ESM-wrapped CommonJS module but it doesn't."
              );
            }),
            (r._require = function (e, r) {
              var t = this._moduleCache[e];
              if (t) return t.exports;
              var o = {
                id: e,
                exports: {},
              };
              return (
                (this._moduleCache[e] = o), this._tryModuleLoad(o, e), o.exports
              );
            }),
            (r._resolve = function (e, r) {
              return (
                this._resolveFromInfos(e, r) || this._throwUnresolved(e, r)
              );
            }),
            (r._resolveFromInfos = function (e, r) {
              var t, o;
              return e in cjsInfos
                ? e
                : r &&
                  null !=
                    (t = null == (o = cjsInfos[r]) ? void 0 : o.resolveCache[e])
                ? t
                : void 0;
            }),
            (r._tryModuleLoad = function (e, r) {
              var t = !0;
              try {
                this._load(e, r), (t = !1);
              } finally {
                t && delete this._moduleCache[r];
              }
            }),
            (r._load = function (e, r) {
              var t = this._loadWrapper(r),
                o = t.factory,
                n = t.resolveMap,
                i = this._createRequire(e),
                u = n
                  ? this._createRequireWithResolveMap(
                      "function" == typeof n ? n() : n,
                      i
                    )
                  : i;
              o(e.exports, u, e);
            }),
            (r._loadWrapper = function (e) {
              return e in this._registry
                ? this._registry[e]
                : this._loadHostProvidedModules(e);
            }),
            (r._loadHostProvidedModules = function (e) {
              return {
                factory: function (r, t, o) {
                  if ("undefined" == typeof require)
                    throw new Error(
                      "Current environment does not provide a require() for requiring '" +
                        e +
                        "'."
                    );
                  try {
                    o.exports = require(e);
                  } catch (r) {
                    throw new Error(
                      "Exception thrown when calling host defined require('" +
                        e +
                        "').",
                      {
                        cause: r,
                      }
                    );
                  }
                },
              };
            }),
            (r._createRequire = function (e) {
              var r = this;
              return function (t) {
                return r._require(t, e);
              };
            }),
            (r._createRequireWithResolveMap = function (e, r) {
              return function (t) {
                var o = e[t];
                if (o) return r(o);
                throw new Error("Unresolved specifier " + t);
              };
            }),
            (r._throwUnresolved = function (e, r) {
              throw new Error(
                "Unable to resolve " + e + " from " + parent + "."
              );
            }),
            e
          );
        })())()
      );
    },
  };
});
