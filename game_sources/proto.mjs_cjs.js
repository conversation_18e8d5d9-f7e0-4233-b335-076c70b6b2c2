System.register(
  [
    process.cwd() + "/game_sources/proto.js",
    process.cwd() + "/game_sources/cjs-loader.mjs",
  ],
  function (t, e) {
    var r, s;
    return {
      setters: [
        function (e) {
          r = e.__cjsMetaURL;
          var s = {};
          (s.__cjsMetaURL = e.__cjsMetaURL), (s.default = e.default), t(s);
        },
        function (t) {
          s = t.default;
        },
      ],
      execute: function () {
        r || s.throwInvalidWrapper("./proto.js", e.meta.url), s.require(r);
      },
    };
  }
);
//
