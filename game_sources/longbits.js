System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/minimal2.js",
  ],
  function (t, i) {
    var n, o;
    return {
      setters: [
        function (t) {
          n = t.default;
        },
        function (t) {
          o = t.__cjsMetaURL;
        },
      ],
      execute: function () {
        var r = t("__cjsMetaURL", i.meta.url);
        n.define(
          r,
          function (t, i, n, o, r) {
            n.exports = h;
            var e = i("../util/minimal");
            function h(t, i) {
              (this.lo = t >>> 0), (this.hi = i >>> 0);
            }
            var s = (h.zero = new h(0, 0));
            (s.toNumber = function () {
              return 0;
            }),
              (s.zzEncode = s.zzDecode =
                function () {
                  return this;
                }),
              (s.length = function () {
                return 1;
              });
            var u = (h.zeroHash = "\0\0\0\0\0\0\0\0");
            (h.fromNumber = function (t) {
              if (0 === t) return s;
              var i = t < 0;
              i && (t = -t);
              var n = t >>> 0,
                o = ((t - n) / 4294967296) >>> 0;
              return (
                i &&
                  ((o = ~o >>> 0),
                  (n = ~n >>> 0),
                  ++n > 4294967295 && ((n = 0), ++o > 4294967295 && (o = 0))),
                new h(n, o)
              );
            }),
              (h.from = function (t) {
                if ("number" == typeof t) return h.fromNumber(t);
                if (e.isString(t)) {
                  if (!e.Long) return h.fromNumber(parseInt(t, 10));
                  t = e.Long.fromString(t);
                }
                return t.low || t.high ? new h(t.low >>> 0, t.high >>> 0) : s;
              }),
              (h.prototype.toNumber = function (t) {
                if (!t && this.hi >>> 31) {
                  var i = (1 + ~this.lo) >>> 0,
                    n = ~this.hi >>> 0;
                  return i || (n = (n + 1) >>> 0), -(i + 4294967296 * n);
                }
                return this.lo + 4294967296 * this.hi;
              }),
              (h.prototype.toLong = function (t) {
                return e.Long
                  ? new e.Long(0 | this.lo, 0 | this.hi, Boolean(t))
                  : {
                      low: 0 | this.lo,
                      high: 0 | this.hi,
                      unsigned: Boolean(t),
                    };
              });
            var l = String.prototype.charCodeAt;
            (h.fromHash = function (t) {
              return t === u
                ? s
                : new h(
                    (l.call(t, 0) |
                      (l.call(t, 1) << 8) |
                      (l.call(t, 2) << 16) |
                      (l.call(t, 3) << 24)) >>>
                      0,
                    (l.call(t, 4) |
                      (l.call(t, 5) << 8) |
                      (l.call(t, 6) << 16) |
                      (l.call(t, 7) << 24)) >>>
                      0
                  );
            }),
              (h.prototype.toHash = function () {
                return String.fromCharCode(
                  255 & this.lo,
                  (this.lo >>> 8) & 255,
                  (this.lo >>> 16) & 255,
                  this.lo >>> 24,
                  255 & this.hi,
                  (this.hi >>> 8) & 255,
                  (this.hi >>> 16) & 255,
                  this.hi >>> 24
                );
              }),
              (h.prototype.zzEncode = function () {
                var t = this.hi >> 31;
                return (
                  (this.hi = (((this.hi << 1) | (this.lo >>> 31)) ^ t) >>> 0),
                  (this.lo = ((this.lo << 1) ^ t) >>> 0),
                  this
                );
              }),
              (h.prototype.zzDecode = function () {
                var t = -(1 & this.lo);
                return (
                  (this.lo = (((this.lo >>> 1) | (this.hi << 31)) ^ t) >>> 0),
                  (this.hi = ((this.hi >>> 1) ^ t) >>> 0),
                  this
                );
              }),
              (h.prototype.length = function () {
                var t = this.lo,
                  i = ((this.lo >>> 28) | (this.hi << 4)) >>> 0,
                  n = this.hi >>> 24;
                return 0 === n
                  ? 0 === i
                    ? t < 16384
                      ? t < 128
                        ? 1
                        : 2
                      : t < 2097152
                      ? 3
                      : 4
                    : i < 16384
                    ? i < 128
                      ? 5
                      : 6
                    : i < 2097152
                    ? 7
                    : 8
                  : n < 128
                  ? 9
                  : 10;
              }),
              n.exports;
          },
          function () {
            return {
              "../util/minimal": o,
            };
          }
        );
      },
    };
  }
);
