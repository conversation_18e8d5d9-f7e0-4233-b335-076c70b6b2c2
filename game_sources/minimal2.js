System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/index3.js",
    process.cwd() + "/game_sources/index2.js",
    process.cwd() + "/game_sources/index.js",
    process.cwd() + "/game_sources/index4.js",
    process.cwd() + "/game_sources/index5.js",
    process.cwd() + "/game_sources/index6.js",
    process.cwd() + "/game_sources/index7.js",
    process.cwd() + "/game_sources/longbits.js",
  ],
  function (e, r) {
    var t, n, o, f, i, u, s, a, c;
    return {
      setters: [
        function (e) {
          t = e.default;
        },
        function (e) {
          n = e.__cjsMetaURL;
        },
        function (e) {
          o = e.__cjsMetaURL;
        },
        function (e) {
          f = e.__cjsMetaURL;
        },
        function (e) {
          i = e.__cjsMetaURL;
        },
        function (e) {
          u = e.__cjsMetaURL;
        },
        function (e) {
          s = e.__cjsMetaURL;
        },
        function (e) {
          a = e.__cjsMetaURL;
        },
        function (e) {
          c = e.__cjsMetaURL;
        },
      ],
      execute: function () {
        var l = e("__cjsMetaURL", r.meta.url);
        t.define(
          l,
          function (e, r, t, n, o) {
            var f = e;
            function i(e, r, t) {
              for (var n = Object.keys(r), o = 0; o < n.length; ++o)
                (void 0 !== e[n[o]] && t) || (e[n[o]] = r[n[o]]);
              return e;
            }
            function u(e) {
              function r(e, t) {
                if (!(this instanceof r)) return new r(e, t);
                Object.defineProperty(this, "message", {
                  get: function () {
                    return e;
                  },
                }),
                  Error.captureStackTrace
                    ? Error.captureStackTrace(this, r)
                    : Object.defineProperty(this, "stack", {
                        value: new Error().stack || "",
                      }),
                  t && i(this, t);
              }
              return (
                (r.prototype = Object.create(Error.prototype, {
                  constructor: {
                    value: r,
                    writable: !0,
                    enumerable: !1,
                    configurable: !0,
                  },
                  name: {
                    get: function () {
                      return e;
                    },
                    set: void 0,
                    enumerable: !1,
                    configurable: !0,
                  },
                  toString: {
                    value: function () {
                      return this.name + ": " + this.message;
                    },
                    writable: !0,
                    enumerable: !1,
                    configurable: !0,
                  },
                })),
                r
              );
            }
            (f.asPromise = r("@protobufjs/aspromise")),
              (f.base64 = r("@protobufjs/base64")),
              (f.EventEmitter = r("@protobufjs/eventemitter")),
              (f.float = r("@protobufjs/float")),
              (f.inquire = r("@protobufjs/inquire")),
              (f.utf8 = r("@protobufjs/utf8")),
              (f.pool = r("@protobufjs/pool")),
              (f.LongBits = r("./longbits")),
              (f.isNode = Boolean(
                "undefined" != typeof global &&
                  global &&
                  global.process &&
                  global.process.versions &&
                  global.process.versions.node
              )),
              (f.global =
                (f.isNode && global) ||
                ("undefined" != typeof window && window) ||
                ("undefined" != typeof self && self) ||
                this),
              (f.emptyArray = Object.freeze ? Object.freeze([]) : []),
              (f.emptyObject = Object.freeze ? Object.freeze({}) : {}),
              (f.isInteger =
                Number.isInteger ||
                function (e) {
                  return (
                    "number" == typeof e && isFinite(e) && Math.floor(e) === e
                  );
                }),
              (f.isString = function (e) {
                return "string" == typeof e || e instanceof String;
              }),
              (f.isObject = function (e) {
                return e && "object" == typeof e;
              }),
              (f.isset = f.isSet =
                function (e, r) {
                  var t = e[r];
                  return (
                    !(null == t || !e.hasOwnProperty(r)) &&
                    ("object" != typeof t ||
                      (Array.isArray(t) ? t.length : Object.keys(t).length) > 0)
                  );
                }),
              (f.Buffer = (function () {
                try {
                  var e = f.inquire("buffer").Buffer;
                  return e.prototype.utf8Write ? e : null;
                } catch (e) {
                  return null;
                }
              })()),
              (f._Buffer_from = null),
              (f._Buffer_allocUnsafe = null),
              (f.newBuffer = function (e) {
                return "number" == typeof e
                  ? f.Buffer
                    ? f._Buffer_allocUnsafe(e)
                    : new f.Array(e)
                  : f.Buffer
                  ? f._Buffer_from(e)
                  : "undefined" == typeof Uint8Array
                  ? e
                  : new Uint8Array(e);
              }),
              (f.Array = "undefined" != typeof Uint8Array ? Uint8Array : Array),
              (f.Long =
                (f.global.dcodeIO && f.global.dcodeIO.Long) ||
                f.global.Long ||
                f.inquire("long")),
              (f.key2Re = /^true|false|0|1$/),
              (f.key32Re = /^-?(?:0|[1-9][0-9]*)$/),
              (f.key64Re = /^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/),
              (f.longToHash = function (e) {
                return e ? f.LongBits.from(e).toHash() : f.LongBits.zeroHash;
              }),
              (f.longFromHash = function (e, r) {
                var t = f.LongBits.fromHash(e);
                return f.Long
                  ? f.Long.fromBits(t.lo, t.hi, r)
                  : t.toNumber(Boolean(r));
              }),
              (f.merge = i),
              (f.lcFirst = function (e) {
                return e.charAt(0).toLowerCase() + e.substring(1);
              }),
              (f.newError = u),
              (f.ProtocolError = u("ProtocolError")),
              (f.oneOfGetter = function (e) {
                for (var r = {}, t = 0; t < e.length; ++t) r[e[t]] = 1;
                return function () {
                  for (var e = Object.keys(this), t = e.length - 1; t > -1; --t)
                    if (
                      1 === r[e[t]] &&
                      void 0 !== this[e[t]] &&
                      null !== this[e[t]]
                    )
                      return e[t];
                };
              }),
              (f.oneOfSetter = function (e) {
                return function (r) {
                  for (var t = 0; t < e.length; ++t)
                    e[t] !== r && delete this[e[t]];
                };
              }),
              (f.toJSONOptions = {
                longs: String,
                enums: String,
                bytes: String,
                json: !0,
              }),
              (f._configure = function () {
                var e = f.Buffer;
                e
                  ? ((f._Buffer_from =
                      (e.from !== Uint8Array.from && e.from) ||
                      function (r, t) {
                        return new e(r, t);
                      }),
                    (f._Buffer_allocUnsafe =
                      e.allocUnsafe ||
                      function (r) {
                        return new e(r);
                      }))
                  : (f._Buffer_from = f._Buffer_allocUnsafe = null);
              }),
              t.exports;
          },
          function () {
            return {
              "@protobufjs/aspromise": n,
              "@protobufjs/base64": o,
              "@protobufjs/eventemitter": f,
              "@protobufjs/float": i,
              "@protobufjs/inquire": u,
              "@protobufjs/utf8": s,
              "@protobufjs/pool": a,
              "./longbits": c,
            };
          }
        );
      },
    };
  }
);
