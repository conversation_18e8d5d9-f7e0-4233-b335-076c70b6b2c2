System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (e, n) {
    var t;
    return {
      setters: [
        function (e) {
          t = e.default;
        },
      ],
      execute: function () {
        var r = e("__cjsMetaURL", n.meta.url);
        t.define(
          r,
          function (e, n, t, r, u) {
            (t.exports = function (e, n) {
              var t = new Array(arguments.length - 1),
                r = 0,
                u = 2,
                l = !0;
              for (; u < arguments.length; ) t[r++] = arguments[u++];
              return new Promise(function (u, a) {
                t[r] = function (e) {
                  if (l)
                    if (((l = !1), e)) a(e);
                    else {
                      for (
                        var n = new Array(arguments.length - 1), t = 0;
                        t < n.length;

                      )
                        n[t++] = arguments[t];
                      u.apply(null, n);
                    }
                };
                try {
                  e.apply(n || null, t);
                } catch (e) {
                  l && ((l = !1), a(e));
                }
              });
            }),
              t.exports;
          },
          {}
        );
      },
    };
  }
);
