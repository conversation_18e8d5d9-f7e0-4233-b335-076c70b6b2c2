System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/minimal2.js",
  ],
  function (t, n) {
    var i, e;
    return {
      setters: [
        function (t) {
          i = t.default;
        },
        function (t) {
          e = t.__cjsMetaURL;
        },
      ],
      execute: function () {
        var o = t("__cjsMetaURL", n.meta.url);
        i.define(
          o,
          function (t, n, i, e, o) {
            i.exports = c;
            var r,
              s = n("./util/minimal"),
              h = s.LongBits,
              u = s.base64,
              a = s.utf8;
            function l(t, n, i) {
              (this.fn = t),
                (this.len = n),
                (this.next = void 0),
                (this.val = i);
            }
            function p() {}
            function f(t) {
              (this.head = t.head),
                (this.tail = t.tail),
                (this.len = t.len),
                (this.next = t.states);
            }
            function c() {
              (this.len = 0),
                (this.head = new l(p, 0, 0)),
                (this.tail = this.head),
                (this.states = null);
            }
            var y = function () {
              return s.Buffer
                ? function () {
                    return (c.create = function () {
                      return new r();
                    })();
                  }
                : function () {
                    return new c();
                  };
            };
            function d(t, n, i) {
              n[i] = 255 & t;
            }
            function _(t, n) {
              (this.len = t), (this.next = void 0), (this.val = n);
            }
            function v(t, n, i) {
              for (; t.hi; )
                (n[i++] = (127 & t.lo) | 128),
                  (t.lo = ((t.lo >>> 7) | (t.hi << 25)) >>> 0),
                  (t.hi >>>= 7);
              for (; t.lo > 127; )
                (n[i++] = (127 & t.lo) | 128), (t.lo = t.lo >>> 7);
              n[i++] = t.lo;
            }
            function x(t, n, i) {
              (n[i] = 255 & t),
                (n[i + 1] = (t >>> 8) & 255),
                (n[i + 2] = (t >>> 16) & 255),
                (n[i + 3] = t >>> 24);
            }
            (c.create = y()),
              (c.alloc = function (t) {
                return new s.Array(t);
              }),
              s.Array !== Array &&
                (c.alloc = s.pool(c.alloc, s.Array.prototype.subarray)),
              (c.prototype._push = function (t, n, i) {
                return (
                  (this.tail = this.tail.next = new l(t, n, i)),
                  (this.len += n),
                  this
                );
              }),
              (_.prototype = Object.create(l.prototype)),
              (_.prototype.fn = function (t, n, i) {
                for (; t > 127; ) (n[i++] = (127 & t) | 128), (t >>>= 7);
                n[i] = t;
              }),
              (c.prototype.uint32 = function (t) {
                return (
                  (this.len += (this.tail = this.tail.next =
                    new _(
                      (t >>>= 0) < 128
                        ? 1
                        : t < 16384
                        ? 2
                        : t < 2097152
                        ? 3
                        : t < 268435456
                        ? 4
                        : 5,
                      t
                    )).len),
                  this
                );
              }),
              (c.prototype.int32 = function (t) {
                return t < 0
                  ? this._push(v, 10, h.fromNumber(t))
                  : this.uint32(t);
              }),
              (c.prototype.sint32 = function (t) {
                return this.uint32(((t << 1) ^ (t >> 31)) >>> 0);
              }),
              (c.prototype.uint64 = function (t) {
                var n = h.from(t);
                return this._push(v, n.length(), n);
              }),
              (c.prototype.int64 = c.prototype.uint64),
              (c.prototype.sint64 = function (t) {
                var n = h.from(t).zzEncode();
                return this._push(v, n.length(), n);
              }),
              (c.prototype.bool = function (t) {
                return this._push(d, 1, t ? 1 : 0);
              }),
              (c.prototype.fixed32 = function (t) {
                return this._push(x, 4, t >>> 0);
              }),
              (c.prototype.sfixed32 = c.prototype.fixed32),
              (c.prototype.fixed64 = function (t) {
                var n = h.from(t);
                return this._push(x, 4, n.lo)._push(x, 4, n.hi);
              }),
              (c.prototype.sfixed64 = c.prototype.fixed64),
              (c.prototype.float = function (t) {
                return this._push(s.float.writeFloatLE, 4, t);
              }),
              (c.prototype.double = function (t) {
                return this._push(s.float.writeDoubleLE, 8, t);
              });
            var m = s.Array.prototype.set
              ? function (t, n, i) {
                  n.set(t, i);
                }
              : function (t, n, i) {
                  for (var e = 0; e < t.length; ++e) n[i + e] = t[e];
                };
            (c.prototype.bytes = function (t) {
              var n = t.length >>> 0;
              if (!n) return this._push(d, 1, 0);
              if (s.isString(t)) {
                var i = c.alloc((n = u.length(t)));
                u.decode(t, i, 0), (t = i);
              }
              return this.uint32(n)._push(m, n, t);
            }),
              (c.prototype.string = function (t) {
                var n = a.length(t);
                return n
                  ? this.uint32(n)._push(a.write, n, t)
                  : this._push(d, 1, 0);
              }),
              (c.prototype.fork = function () {
                return (
                  (this.states = new f(this)),
                  (this.head = this.tail = new l(p, 0, 0)),
                  (this.len = 0),
                  this
                );
              }),
              (c.prototype.reset = function () {
                return (
                  this.states
                    ? ((this.head = this.states.head),
                      (this.tail = this.states.tail),
                      (this.len = this.states.len),
                      (this.states = this.states.next))
                    : ((this.head = this.tail = new l(p, 0, 0)),
                      (this.len = 0)),
                  this
                );
              }),
              (c.prototype.ldelim = function () {
                var t = this.head,
                  n = this.tail,
                  i = this.len;
                return (
                  this.reset().uint32(i),
                  i &&
                    ((this.tail.next = t.next),
                    (this.tail = n),
                    (this.len += i)),
                  this
                );
              }),
              (c.prototype.finish = function () {
                for (
                  var t = this.head.next,
                    n = this.constructor.alloc(this.len),
                    i = 0;
                  t;

                )
                  t.fn(t.val, n, i), (i += t.len), (t = t.next);
                return n;
              }),
              (c._configure = function (t) {
                (r = t), (c.create = y()), r._configure();
              }),
              i.exports;
          },
          function () {
            return {
              "./util/minimal": e,
            };
          }
        );
      },
    };
  }
);
