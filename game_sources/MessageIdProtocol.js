System.register(
  [
    process.cwd() + "/game_sources/MessageId.js",
    process.cwd() + "/game_sources/proto.mjs_cjs.js",
    process.cwd() + "/game_sources/proto.js",
  ],
  function (e) {
    var d, o, c;
    return {
      setters: [
        function (e) {
          o = e.default;
        },
        null,
        function (e) {
          c = e.default;
        },
      ],
      execute: function () {
        var m = e("default", new Map());
        m.set(o.Login<PERSON>ogin, {
          encode: c.msg.LoginLogin.encode,
          decode: c.msg.LoginLogin.decode,
        }),
          m.set(o<PERSON><PERSON><PERSON>, {
            encode: c.msg.LoginFail.encode,
            decode: c.msg.LoginFail.decode,
          }),
          m.set(o.LogicGMCmd, {
            encode: c.msg.LogicGMCmd.encode,
            decode: c.msg.LogicGMCmd.decode,
          }),
          m.set(o.LogicSetMain, {
            encode: c.msg.LogicSetMain.encode,
            decode: c.msg.LogicSetMain.decode,
          }),
          m.set(o.LogicVitUpdate, {
            encode: c.msg.LogicVitUpdate.encode,
            decode: c.msg.LogicVitUpdate.decode,
          }),
          m.set(o.LogicActivityUpdate, {
            encode: c.msg.LogicActivityUpdate.encode,
            decode: c.msg.LogicActivityUpdate.decode,
          }),
          m.set(o.HeroVitUpdate, {
            encode: c.msg.HeroVitUpdate.encode,
            decode: c.msg.HeroVitUpdate.decode,
          }),
          m.set(o.EquipmentMerge, {
            encode: c.msg.EquipmentMerge.encode,
            decode: c.msg.EquipmentMerge.decode,
          }),
          m.set(o.EquipmentLink, {
            encode: c.msg.EquipmentLink.encode,
            decode: c.msg.EquipmentLink.decode,
          }),
          m.set(o.EquipmentRepair, {
            encode: c.msg.EquipmentRepair.encode,
            decode: c.msg.EquipmentRepair.decode,
          }),
          m.set(o.GameStart, {
            encode: c.msg.GameStart.encode,
            decode: c.msg.GameStart.decode,
          }),
          m.set(o.GameLoaded, {
            encode: c.msg.GameLoaded.encode,
            decode: c.msg.GameLoaded.decode,
          }),
          m.set(o.GameDone, {
            encode: c.msg.GameDone.encode,
            decode: c.msg.GameDone.decode,
          }),
          m.set(o.GamePause, {
            encode: c.msg.GamePause.encode,
            decode: c.msg.GamePause.decode,
          }),
          m.set(o.GameShopOperate, {
            encode: c.msg.GameShopOperate.encode,
            decode: c.msg.GameShopOperate.decode,
          }),
          m.set(o.GameQuit, {
            encode: c.msg.GameQuit.encode,
            decode: c.msg.GameQuit.decode,
          }),
          m.set(o.GameSpawn, {
            encode: c.msg.GameSpawn.encode,
            decode: c.msg.GameSpawn.decode,
          }),
          m.set(o.GameHeartbeat, {
            encode: c.msg.GameHeartbeat.encode,
            decode: c.msg.GameHeartbeat.decode,
          }),
          m.set(o.GameMonsterAtk, {
            encode: c.msg.GameMonsterAtk.encode,
            decode: c.msg.GameMonsterAtk.decode,
          }),
          m.set(o.GameCharAtk, {
            encode: c.msg.GameCharAtk.encode,
            decode: c.msg.GameCharAtk.decode,
          }),
          m.set(o.GamePick, {
            encode: c.msg.GamePick.encode,
            decode: c.msg.GamePick.decode,
          }),
          m.set(o.GameDmgEx, {
            encode: c.msg.GameDmgEx.encode,
            decode: c.msg.GameDmgEx.decode,
          }),
          m.set(o.GameWeaponUpdate, {
            encode: c.msg.GameWeaponUpdate.encode,
            decode: c.msg.GameWeaponUpdate.decode,
          }),
          m.set(o.GameLifeRecover, {
            encode: c.msg.GameLifeRecover.encode,
            decode: c.msg.GameLifeRecover.decode,
          }),
          m.set(o.CommonClientErr, {
            encode: c.msg.CommonClientErr.encode,
            decode: c.msg.CommonClientErr.decode,
          }),
          m.set(o.CommonHeartbeat, {
            encode: c.msg.CommonHeartbeat.encode,
            decode: c.msg.CommonHeartbeat.decode,
          }),
          m.set(o.ResponseDesc, {
            encode: c.msg.ResponseDesc.encode,
            decode: c.msg.ResponseDesc.decode,
          }),
          m.set(o.CommonHandShake, {
            encode: c.msg.CommonHandShake.encode,
            decode: c.msg.CommonHandShake.decode,
          });
      },
    };
  }
);
