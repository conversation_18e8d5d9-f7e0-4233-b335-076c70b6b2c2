System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/reader.js",
    process.cwd() + "/game_sources/minimal2.js",
  ],
  function (t, e) {
    var i, r, n;
    return {
      setters: [
        function (t) {
          i = t.default;
        },
        function (t) {
          r = t.__cjsMetaURL;
        },
        function (t) {
          n = t.__cjsMetaURL;
        },
      ],
      execute: function () {
        var s = t("__cjsMetaURL", e.meta.url);
        i.define(
          s,
          function (t, e, i, r, n) {
            i.exports = u;
            var s = e("./reader");
            (u.prototype = Object.create(s.prototype)).constructor = u;
            var o = e("./util/minimal");
            function u(t) {
              s.call(this, t);
            }
            (u._configure = function () {
              o.Buffer && (u.prototype._slice = o.Buffer.prototype.slice);
            }),
              (u.prototype.string = function () {
                var t = this.uint32();
                return this.buf.utf8Slice
                  ? this.buf.utf8Slice(
                      this.pos,
                      (this.pos = Math.min(this.pos + t, this.len))
                    )
                  : this.buf.toString(
                      "utf-8",
                      this.pos,
                      (this.pos = Math.min(this.pos + t, this.len))
                    );
              }),
              u._configure(),
              i.exports;
          },
          function () {
            return {
              "./reader": r,
              "./util/minimal": n,
            };
          }
        );
      },
    };
  }
);
