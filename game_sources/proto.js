System.register(
  [
    process.cwd() + "/game_sources/cjs-loader.mjs",
    process.cwd() + "/game_sources/minimal.js",
  ],

  function (e, t) {
    var r, n;
    return {
      setters: [
        function (e) {
          r = e.default;
        },
        function (e) {
          n = e.__cjsMetaURL;
        },
      ],
      execute: function () {
        e("default", void 0);
        var o = e("__cjsMetaURL", t.meta.url);
        r.define(
          o,
          function (t, r, n, o, i) {
            var a,
              l,
              s,
              c = r("protobufjs/minimal.js"),
              u = c.Reader,
              p = c.Writer,
              f = c.util,
              y = c.roots.default || (c.roots.default = {});
            (y.msg =
              (((s = {}).CommonClientErr = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Dump = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Dump &&
                        Object.hasOwnProperty.call(e, "Dump") &&
                        t.uint32(10).string(e.Dump),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.CommonClientErr();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Dump = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Dump &&
                        e.hasOwnProperty("Dump") &&
                        !f.isString(e.Dump)
                      ? "Dump: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.CommonClientErr) return e;
                    var t = new y.msg.CommonClientErr();
                    return null != e.Dump && (t.Dump = String(e.Dump)), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Dump = ""),
                      null != e.Dump &&
                        e.hasOwnProperty("Dump") &&
                        (r.Dump = e.Dump),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.CommonHeartbeat = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Cnt = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Cnt &&
                        Object.hasOwnProperty.call(e, "Cnt") &&
                        t.uint32(8).int32(e.Cnt),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.CommonHeartbeat();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Cnt = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Cnt &&
                        e.hasOwnProperty("Cnt") &&
                        !f.isInteger(e.Cnt)
                      ? "Cnt: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.CommonHeartbeat) return e;
                    var t = new y.msg.CommonHeartbeat();
                    return null != e.Cnt && (t.Cnt = 0 | e.Cnt), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Cnt = 0),
                      null != e.Cnt &&
                        e.hasOwnProperty("Cnt") &&
                        (r.Cnt = e.Cnt),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.CommonHandShake = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.TS = 0),
                  (e.prototype.Sign = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.TS &&
                        Object.hasOwnProperty.call(e, "TS") &&
                        t.uint32(8).int32(e.TS),
                      null != e.Sign &&
                        Object.hasOwnProperty.call(e, "Sign") &&
                        t.uint32(18).string(e.Sign),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.CommonHandShake();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.TS = e.int32();
                          break;
                        case 2:
                          n.Sign = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.TS &&
                        e.hasOwnProperty("TS") &&
                        !f.isInteger(e.TS)
                      ? "TS: integer expected"
                      : null != e.Sign &&
                        e.hasOwnProperty("Sign") &&
                        !f.isString(e.Sign)
                      ? "Sign: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.CommonHandShake) return e;
                    var t = new y.msg.CommonHandShake();
                    return (
                      null != e.TS && (t.TS = 0 | e.TS),
                      null != e.Sign && (t.Sign = String(e.Sign)),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.TS = 0), (r.Sign = "")),
                      null != e.TS && e.hasOwnProperty("TS") && (r.TS = e.TS),
                      null != e.Sign &&
                        e.hasOwnProperty("Sign") &&
                        (r.Sign = e.Sign),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.ResponseDesc = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Code = 0),
                  (e.prototype.Msg = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Code &&
                        Object.hasOwnProperty.call(e, "Code") &&
                        t.uint32(8).int32(e.Code),
                      null != e.Msg &&
                        Object.hasOwnProperty.call(e, "Msg") &&
                        t.uint32(18).string(e.Msg),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.ResponseDesc();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Code = e.int32();
                          break;
                        case 2:
                          n.Msg = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Code && e.hasOwnProperty("Code"))
                      switch (e.Code) {
                        default:
                          return "Code: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                        case 7:
                        case 8:
                        case 9:
                        case 10:
                      }
                    return null != e.Msg &&
                      e.hasOwnProperty("Msg") &&
                      !f.isString(e.Msg)
                      ? "Msg: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.ResponseDesc) return e;
                    var t = new y.msg.ResponseDesc();
                    switch (e.Code) {
                      case "RC_Success":
                      case 0:
                        t.Code = 0;
                        break;
                      case "RC_UnknowErr":
                      case 1:
                        t.Code = 1;
                        break;
                      case "RC_Sign":
                      case 2:
                        t.Code = 2;
                        break;
                      case "RC_ParamLack":
                      case 3:
                        t.Code = 3;
                        break;
                      case "RC_DBErr":
                      case 4:
                        t.Code = 4;
                        break;
                      case "RC_RedisErr":
                      case 5:
                        t.Code = 5;
                        break;
                      case "RC_SessionErr":
                      case 6:
                        t.Code = 6;
                        break;
                      case "RC_ParamErr":
                      case 7:
                        t.Code = 7;
                        break;
                      case "RC_NoRight":
                      case 8:
                        t.Code = 8;
                        break;
                      case "RC_LogicErr":
                      case 9:
                        t.Code = 9;
                        break;
                      case "RC_SveTips":
                      case 10:
                        t.Code = 10;
                    }
                    return null != e.Msg && (t.Msg = String(e.Msg)), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.Code = t.enums === String ? "RC_Success" : 0),
                        (r.Msg = "")),
                      null != e.Code &&
                        e.hasOwnProperty("Code") &&
                        (r.Code =
                          t.enums === String
                            ? y.msg.ResponseCode[e.Code]
                            : e.Code),
                      null != e.Msg &&
                        e.hasOwnProperty("Msg") &&
                        (r.Msg = e.Msg),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LoginLogin = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Code = ""),
                  (e.prototype.OpenID = ""),
                  (e.prototype.OS = ""),
                  (e.prototype.Avatar = ""),
                  (e.prototype.Nick = ""),
                  (e.prototype.Plr = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Code &&
                        Object.hasOwnProperty.call(e, "Code") &&
                        t.uint32(10).string(e.Code),
                      null != e.OpenID &&
                        Object.hasOwnProperty.call(e, "OpenID") &&
                        t.uint32(18).string(e.OpenID),
                      null != e.OS &&
                        Object.hasOwnProperty.call(e, "OS") &&
                        t.uint32(26).string(e.OS),
                      null != e.Avatar &&
                        Object.hasOwnProperty.call(e, "Avatar") &&
                        t.uint32(34).string(e.Avatar),
                      null != e.Nick &&
                        Object.hasOwnProperty.call(e, "Nick") &&
                        t.uint32(42).string(e.Nick),
                      null != e.Plr &&
                        Object.hasOwnProperty.call(e, "Plr") &&
                        y.msg.PlayerData.encode(
                          e.Plr,
                          t.uint32(50).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LoginLogin();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Code = e.string();
                          break;
                        case 2:
                          n.OpenID = e.string();
                          break;
                        case 3:
                          n.OS = e.string();
                          break;
                        case 4:
                          n.Avatar = e.string();
                          break;
                        case 5:
                          n.Nick = e.string();
                          break;
                        case 6:
                          n.Plr = y.msg.PlayerData.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Code &&
                      e.hasOwnProperty("Code") &&
                      !f.isString(e.Code)
                    )
                      return "Code: string expected";
                    if (
                      null != e.OpenID &&
                      e.hasOwnProperty("OpenID") &&
                      !f.isString(e.OpenID)
                    )
                      return "OpenID: string expected";
                    if (
                      null != e.OS &&
                      e.hasOwnProperty("OS") &&
                      !f.isString(e.OS)
                    )
                      return "OS: string expected";
                    if (
                      null != e.Avatar &&
                      e.hasOwnProperty("Avatar") &&
                      !f.isString(e.Avatar)
                    )
                      return "Avatar: string expected";
                    if (
                      null != e.Nick &&
                      e.hasOwnProperty("Nick") &&
                      !f.isString(e.Nick)
                    )
                      return "Nick: string expected";
                    if (null != e.Plr && e.hasOwnProperty("Plr")) {
                      var t = y.msg.PlayerData.verify(e.Plr);
                      if (t) return "Plr." + t;
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LoginLogin) return e;
                    var t = new y.msg.LoginLogin();
                    if (
                      (null != e.Code && (t.Code = String(e.Code)),
                      null != e.OpenID && (t.OpenID = String(e.OpenID)),
                      null != e.OS && (t.OS = String(e.OS)),
                      null != e.Avatar && (t.Avatar = String(e.Avatar)),
                      null != e.Nick && (t.Nick = String(e.Nick)),
                      null != e.Plr)
                    ) {
                      if ("object" != typeof e.Plr)
                        throw TypeError(".msg.LoginLogin.Plr: object expected");
                      t.Plr = y.msg.PlayerData.fromObject(e.Plr);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.Code = ""),
                        (r.OpenID = ""),
                        (r.OS = ""),
                        (r.Avatar = ""),
                        (r.Nick = ""),
                        (r.Plr = null)),
                      null != e.Code &&
                        e.hasOwnProperty("Code") &&
                        (r.Code = e.Code),
                      null != e.OpenID &&
                        e.hasOwnProperty("OpenID") &&
                        (r.OpenID = e.OpenID),
                      null != e.OS && e.hasOwnProperty("OS") && (r.OS = e.OS),
                      null != e.Avatar &&
                        e.hasOwnProperty("Avatar") &&
                        (r.Avatar = e.Avatar),
                      null != e.Nick &&
                        e.hasOwnProperty("Nick") &&
                        (r.Nick = e.Nick),
                      null != e.Plr &&
                        e.hasOwnProperty("Plr") &&
                        (r.Plr = y.msg.PlayerData.toObject(e.Plr, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LoginFail = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.Tips = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.Tips &&
                        Object.hasOwnProperty.call(e, "Tips") &&
                        t.uint32(18).string(e.Tips),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LoginFail();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          n.Tips = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        !f.isInteger(e.Type)
                      ? "Type: integer expected"
                      : null != e.Tips &&
                        e.hasOwnProperty("Tips") &&
                        !f.isString(e.Tips)
                      ? "Tips: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LoginFail) return e;
                    var t = new y.msg.LoginFail();
                    return (
                      null != e.Type && (t.Type = 0 | e.Type),
                      null != e.Tips && (t.Tips = String(e.Tips)),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.Type = 0), (r.Tips = "")),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      null != e.Tips &&
                        e.hasOwnProperty("Tips") &&
                        (r.Tips = e.Tips),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogicGMCmd = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Cmd = ""),
                  (e.prototype.Hero = null),
                  (e.prototype.Equipment = null),
                  (e.prototype.BUpdate = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Cmd &&
                        Object.hasOwnProperty.call(e, "Cmd") &&
                        t.uint32(10).string(e.Cmd),
                      null != e.Hero &&
                        Object.hasOwnProperty.call(e, "Hero") &&
                        y.msg.HeroToken.encode(
                          e.Hero,
                          t.uint32(18).fork()
                        ).ldelim(),
                      null != e.Equipment &&
                        Object.hasOwnProperty.call(e, "Equipment") &&
                        y.msg.EquipmentToken.encode(
                          e.Equipment,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.BUpdate &&
                        Object.hasOwnProperty.call(e, "BUpdate") &&
                        y.msg.BattleUpdate.encode(
                          e.BUpdate,
                          t.uint32(34).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LogicGMCmd();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Cmd = e.string();
                          break;
                        case 2:
                          n.Hero = y.msg.HeroToken.decode(e, e.uint32());
                          break;
                        case 3:
                          n.Equipment = y.msg.EquipmentToken.decode(
                            e,
                            e.uint32()
                          );
                          break;
                        case 4:
                          n.BUpdate = y.msg.BattleUpdate.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Cmd &&
                        e.hasOwnProperty("Cmd") &&
                        !f.isString(e.Cmd)
                      ? "Cmd: string expected"
                      : null != e.Hero &&
                        e.hasOwnProperty("Hero") &&
                        (t = y.msg.HeroToken.verify(e.Hero))
                      ? "Hero." + t
                      : null != e.Equipment &&
                        e.hasOwnProperty("Equipment") &&
                        (t = y.msg.EquipmentToken.verify(e.Equipment))
                      ? "Equipment." + t
                      : null != e.BUpdate &&
                        e.hasOwnProperty("BUpdate") &&
                        (t = y.msg.BattleUpdate.verify(e.BUpdate))
                      ? "BUpdate." + t
                      : null;
                    var t;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LogicGMCmd) return e;
                    var t = new y.msg.LogicGMCmd();
                    if (
                      (null != e.Cmd && (t.Cmd = String(e.Cmd)), null != e.Hero)
                    ) {
                      if ("object" != typeof e.Hero)
                        throw TypeError(
                          ".msg.LogicGMCmd.Hero: object expected"
                        );
                      t.Hero = y.msg.HeroToken.fromObject(e.Hero);
                    }
                    if (null != e.Equipment) {
                      if ("object" != typeof e.Equipment)
                        throw TypeError(
                          ".msg.LogicGMCmd.Equipment: object expected"
                        );
                      t.Equipment = y.msg.EquipmentToken.fromObject(
                        e.Equipment
                      );
                    }
                    if (null != e.BUpdate) {
                      if ("object" != typeof e.BUpdate)
                        throw TypeError(
                          ".msg.LogicGMCmd.BUpdate: object expected"
                        );
                      t.BUpdate = y.msg.BattleUpdate.fromObject(e.BUpdate);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.Cmd = ""),
                        (r.Hero = null),
                        (r.Equipment = null),
                        (r.BUpdate = null)),
                      null != e.Cmd &&
                        e.hasOwnProperty("Cmd") &&
                        (r.Cmd = e.Cmd),
                      null != e.Hero &&
                        e.hasOwnProperty("Hero") &&
                        (r.Hero = y.msg.HeroToken.toObject(e.Hero, t)),
                      null != e.Equipment &&
                        e.hasOwnProperty("Equipment") &&
                        (r.Equipment = y.msg.EquipmentToken.toObject(
                          e.Equipment,
                          t
                        )),
                      null != e.BUpdate &&
                        e.hasOwnProperty("BUpdate") &&
                        (r.BUpdate = y.msg.BattleUpdate.toObject(e.BUpdate, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogicSetMain = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.HeroSN = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.HeroSN &&
                        Object.hasOwnProperty.call(e, "HeroSN") &&
                        t.uint32(8).uint32(e.HeroSN),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LogicSetMain();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.HeroSN = e.uint32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.HeroSN &&
                        e.hasOwnProperty("HeroSN") &&
                        !f.isInteger(e.HeroSN)
                      ? "HeroSN: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LogicSetMain) return e;
                    var t = new y.msg.LogicSetMain();
                    return null != e.HeroSN && (t.HeroSN = e.HeroSN >>> 0), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.HeroSN = 0),
                      null != e.HeroSN &&
                        e.hasOwnProperty("HeroSN") &&
                        (r.HeroSN = e.HeroSN),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogicVitUpdate = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Vit = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Vit &&
                        Object.hasOwnProperty.call(e, "Vit") &&
                        t.uint32(8).int32(e.Vit),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LogicVitUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Vit = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        !f.isInteger(e.Vit)
                      ? "Vit: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LogicVitUpdate) return e;
                    var t = new y.msg.LogicVitUpdate();
                    return null != e.Vit && (t.Vit = 0 | e.Vit), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Vit = 0),
                      null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        (r.Vit = e.Vit),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogicSetSetting = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Setting = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Setting &&
                        Object.hasOwnProperty.call(e, "Setting") &&
                        t.uint32(10).string(e.Setting),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LogicSetSetting();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Setting = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Setting &&
                        e.hasOwnProperty("Setting") &&
                        !f.isString(e.Setting)
                      ? "Setting: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LogicSetSetting) return e;
                    var t = new y.msg.LogicSetSetting();
                    return (
                      null != e.Setting && (t.Setting = String(e.Setting)), t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Setting = ""),
                      null != e.Setting &&
                        e.hasOwnProperty("Setting") &&
                        (r.Setting = e.Setting),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogicActivityUpdate = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Activity = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Activity &&
                        Object.hasOwnProperty.call(e, "Activity") &&
                        t.uint32(8).int32(e.Activity),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.LogicActivityUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Activity = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Activity &&
                        e.hasOwnProperty("Activity") &&
                        !f.isInteger(e.Activity)
                      ? "Activity: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.LogicActivityUpdate) return e;
                    var t = new y.msg.LogicActivityUpdate();
                    return (
                      null != e.Activity && (t.Activity = 0 | e.Activity), t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Activity = 0),
                      null != e.Activity &&
                        e.hasOwnProperty("Activity") &&
                        (r.Activity = e.Activity),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.HeroVitUpdate = (function () {
                function e(e) {
                  if (((this.HVits = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.HVits = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()), null != e.HVits && e.HVits.length)
                    )
                      for (var r = 0; r < e.HVits.length; ++r)
                        y.msg.HeroVit.encode(
                          e.HVits[r],
                          t.uint32(10).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.HeroVitUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          (n.HVits && n.HVits.length) || (n.HVits = []),
                            n.HVits.push(y.msg.HeroVit.decode(e, e.uint32()));
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.HVits && e.hasOwnProperty("HVits")) {
                      if (!Array.isArray(e.HVits))
                        return "HVits: array expected";
                      for (var t = 0; t < e.HVits.length; ++t) {
                        var r = y.msg.HeroVit.verify(e.HVits[t]);
                        if (r) return "HVits." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.HeroVitUpdate) return e;
                    var t = new y.msg.HeroVitUpdate();
                    if (e.HVits) {
                      if (!Array.isArray(e.HVits))
                        throw TypeError(
                          ".msg.HeroVitUpdate.HVits: array expected"
                        );
                      t.HVits = [];
                      for (var r = 0; r < e.HVits.length; ++r) {
                        if ("object" != typeof e.HVits[r])
                          throw TypeError(
                            ".msg.HeroVitUpdate.HVits: object expected"
                          );
                        t.HVits[r] = y.msg.HeroVit.fromObject(e.HVits[r]);
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.HVits = []),
                      e.HVits && e.HVits.length)
                    ) {
                      r.HVits = [];
                      for (var n = 0; n < e.HVits.length; ++n)
                        r.HVits[n] = y.msg.HeroVit.toObject(e.HVits[n], t);
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.EquipmentMerge = (function () {
                function e(e) {
                  if (((this.SNAry = []), (this.MAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.TID = 0),
                  (e.prototype.SNAry = f.emptyArray),
                  (e.prototype.EToken = null),
                  (e.prototype.C2 = ""),
                  (e.prototype.MAry = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.TID &&
                        Object.hasOwnProperty.call(e, "TID") &&
                        t.uint32(8).int32(e.TID),
                      null != e.SNAry && e.SNAry.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.SNAry.length; ++r)
                        t.uint32(e.SNAry[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.EToken &&
                        Object.hasOwnProperty.call(e, "EToken") &&
                        y.msg.EquipmentToken.encode(
                          e.EToken,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(34).string(e.C2),
                      null != e.MAry && e.MAry.length)
                    )
                      for (r = 0; r < e.MAry.length; ++r)
                        y.msg.MaterialToken.encode(
                          e.MAry[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.EquipmentMerge();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.TID = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.SNAry && n.SNAry.length) || (n.SNAry = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.SNAry.push(e.uint32());
                          else n.SNAry.push(e.uint32());
                          break;
                        case 3:
                          n.EToken = y.msg.EquipmentToken.decode(e, e.uint32());
                          break;
                        case 4:
                          n.C2 = e.string();
                          break;
                        case 6:
                          (n.MAry && n.MAry.length) || (n.MAry = []),
                            n.MAry.push(
                              y.msg.MaterialToken.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.TID &&
                      e.hasOwnProperty("TID") &&
                      !f.isInteger(e.TID)
                    )
                      return "TID: integer expected";
                    if (null != e.SNAry && e.hasOwnProperty("SNAry")) {
                      if (!Array.isArray(e.SNAry))
                        return "SNAry: array expected";
                      for (var t = 0; t < e.SNAry.length; ++t)
                        if (!f.isInteger(e.SNAry[t]))
                          return "SNAry: integer[] expected";
                    }
                    if (
                      null != e.EToken &&
                      e.hasOwnProperty("EToken") &&
                      (r = y.msg.EquipmentToken.verify(e.EToken))
                    )
                      return "EToken." + r;
                    if (
                      null != e.C2 &&
                      e.hasOwnProperty("C2") &&
                      !f.isString(e.C2)
                    )
                      return "C2: string expected";
                    if (null != e.MAry && e.hasOwnProperty("MAry")) {
                      if (!Array.isArray(e.MAry)) return "MAry: array expected";
                      for (t = 0; t < e.MAry.length; ++t) {
                        var r;
                        if ((r = y.msg.MaterialToken.verify(e.MAry[t])))
                          return "MAry." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.EquipmentMerge) return e;
                    var t = new y.msg.EquipmentMerge();
                    if ((null != e.TID && (t.TID = 0 | e.TID), e.SNAry)) {
                      if (!Array.isArray(e.SNAry))
                        throw TypeError(
                          ".msg.EquipmentMerge.SNAry: array expected"
                        );
                      t.SNAry = [];
                      for (var r = 0; r < e.SNAry.length; ++r)
                        t.SNAry[r] = e.SNAry[r] >>> 0;
                    }
                    if (null != e.EToken) {
                      if ("object" != typeof e.EToken)
                        throw TypeError(
                          ".msg.EquipmentMerge.EToken: object expected"
                        );
                      t.EToken = y.msg.EquipmentToken.fromObject(e.EToken);
                    }
                    if ((null != e.C2 && (t.C2 = String(e.C2)), e.MAry)) {
                      if (!Array.isArray(e.MAry))
                        throw TypeError(
                          ".msg.EquipmentMerge.MAry: array expected"
                        );
                      for (t.MAry = [], r = 0; r < e.MAry.length; ++r) {
                        if ("object" != typeof e.MAry[r])
                          throw TypeError(
                            ".msg.EquipmentMerge.MAry: object expected"
                          );
                        t.MAry[r] = y.msg.MaterialToken.fromObject(e.MAry[r]);
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.SNAry = []), (r.MAry = [])),
                      t.defaults &&
                        ((r.TID = 0), (r.EToken = null), (r.C2 = "")),
                      null != e.TID &&
                        e.hasOwnProperty("TID") &&
                        (r.TID = e.TID),
                      e.SNAry && e.SNAry.length)
                    ) {
                      r.SNAry = [];
                      for (var n = 0; n < e.SNAry.length; ++n)
                        r.SNAry[n] = e.SNAry[n];
                    }
                    if (
                      (null != e.EToken &&
                        e.hasOwnProperty("EToken") &&
                        (r.EToken = y.msg.EquipmentToken.toObject(e.EToken, t)),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      e.MAry && e.MAry.length)
                    )
                      for (r.MAry = [], n = 0; n < e.MAry.length; ++n)
                        r.MAry[n] = y.msg.MaterialToken.toObject(e.MAry[n], t);
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.EquipmentLink = (function () {
                function e(e) {
                  if (((this.H2EAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.H2EAry = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.H2EAry && e.H2EAry.length)
                    )
                      for (var r = 0; r < e.H2EAry.length; ++r)
                        y.msg.Hero2Equipment.encode(
                          e.H2EAry[r],
                          t.uint32(10).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.EquipmentLink();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          (n.H2EAry && n.H2EAry.length) || (n.H2EAry = []),
                            n.H2EAry.push(
                              y.msg.Hero2Equipment.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.H2EAry && e.hasOwnProperty("H2EAry")) {
                      if (!Array.isArray(e.H2EAry))
                        return "H2EAry: array expected";
                      for (var t = 0; t < e.H2EAry.length; ++t) {
                        var r = y.msg.Hero2Equipment.verify(e.H2EAry[t]);
                        if (r) return "H2EAry." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.EquipmentLink) return e;
                    var t = new y.msg.EquipmentLink();
                    if (e.H2EAry) {
                      if (!Array.isArray(e.H2EAry))
                        throw TypeError(
                          ".msg.EquipmentLink.H2EAry: array expected"
                        );
                      t.H2EAry = [];
                      for (var r = 0; r < e.H2EAry.length; ++r) {
                        if ("object" != typeof e.H2EAry[r])
                          throw TypeError(
                            ".msg.EquipmentLink.H2EAry: object expected"
                          );
                        t.H2EAry[r] = y.msg.Hero2Equipment.fromObject(
                          e.H2EAry[r]
                        );
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.H2EAry = []),
                      e.H2EAry && e.H2EAry.length)
                    ) {
                      r.H2EAry = [];
                      for (var n = 0; n < e.H2EAry.length; ++n)
                        r.H2EAry[n] = y.msg.Hero2Equipment.toObject(
                          e.H2EAry[n],
                          t
                        );
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.EquipmentRepair = (function () {
                function e(e) {
                  if (((this.ESN = []), (this.MAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.ESN = f.emptyArray),
                  (e.prototype.C2 = ""),
                  (e.prototype.MAry = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()), null != e.ESN && e.ESN.length)
                    ) {
                      t.uint32(10).fork();
                      for (var r = 0; r < e.ESN.length; ++r) t.uint32(e.ESN[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(18).string(e.C2),
                      null != e.MAry && e.MAry.length)
                    )
                      for (r = 0; r < e.MAry.length; ++r)
                        y.msg.MaterialToken.encode(
                          e.MAry[r],
                          t.uint32(26).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.EquipmentRepair();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          if (
                            ((n.ESN && n.ESN.length) || (n.ESN = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.ESN.push(e.uint32());
                          else n.ESN.push(e.uint32());
                          break;
                        case 2:
                          n.C2 = e.string();
                          break;
                        case 3:
                          (n.MAry && n.MAry.length) || (n.MAry = []),
                            n.MAry.push(
                              y.msg.MaterialToken.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.ESN && e.hasOwnProperty("ESN")) {
                      if (!Array.isArray(e.ESN)) return "ESN: array expected";
                      for (var t = 0; t < e.ESN.length; ++t)
                        if (!f.isInteger(e.ESN[t]))
                          return "ESN: integer[] expected";
                    }
                    if (
                      null != e.C2 &&
                      e.hasOwnProperty("C2") &&
                      !f.isString(e.C2)
                    )
                      return "C2: string expected";
                    if (null != e.MAry && e.hasOwnProperty("MAry")) {
                      if (!Array.isArray(e.MAry)) return "MAry: array expected";
                      for (t = 0; t < e.MAry.length; ++t) {
                        var r = y.msg.MaterialToken.verify(e.MAry[t]);
                        if (r) return "MAry." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.EquipmentRepair) return e;
                    var t = new y.msg.EquipmentRepair();
                    if (e.ESN) {
                      if (!Array.isArray(e.ESN))
                        throw TypeError(
                          ".msg.EquipmentRepair.ESN: array expected"
                        );
                      t.ESN = [];
                      for (var r = 0; r < e.ESN.length; ++r)
                        t.ESN[r] = e.ESN[r] >>> 0;
                    }
                    if ((null != e.C2 && (t.C2 = String(e.C2)), e.MAry)) {
                      if (!Array.isArray(e.MAry))
                        throw TypeError(
                          ".msg.EquipmentRepair.MAry: array expected"
                        );
                      for (t.MAry = [], r = 0; r < e.MAry.length; ++r) {
                        if ("object" != typeof e.MAry[r])
                          throw TypeError(
                            ".msg.EquipmentRepair.MAry: object expected"
                          );
                        t.MAry[r] = y.msg.MaterialToken.fromObject(e.MAry[r]);
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.ESN = []), (r.MAry = [])),
                      t.defaults && (r.C2 = ""),
                      e.ESN && e.ESN.length)
                    ) {
                      r.ESN = [];
                      for (var n = 0; n < e.ESN.length; ++n)
                        r.ESN[n] = e.ESN[n];
                    }
                    if (
                      (null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      e.MAry && e.MAry.length)
                    )
                      for (r.MAry = [], n = 0; n < e.MAry.length; ++n)
                        r.MAry[n] = y.msg.MaterialToken.toObject(e.MAry[n], t);
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.StoreBuy = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.MID = 0),
                  (e.prototype.Num = 0),
                  (e.prototype.MToken = null),
                  (e.prototype.C2 = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.MID &&
                        Object.hasOwnProperty.call(e, "MID") &&
                        t.uint32(8).int32(e.MID),
                      null != e.Num &&
                        Object.hasOwnProperty.call(e, "Num") &&
                        t.uint32(16).int32(e.Num),
                      null != e.MToken &&
                        Object.hasOwnProperty.call(e, "MToken") &&
                        y.msg.MaterialToken.encode(
                          e.MToken,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(34).string(e.C2),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.StoreBuy();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.MID = e.int32();
                          break;
                        case 2:
                          n.Num = e.int32();
                          break;
                        case 3:
                          n.MToken = y.msg.MaterialToken.decode(e, e.uint32());
                          break;
                        case 4:
                          n.C2 = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.MID &&
                      e.hasOwnProperty("MID") &&
                      !f.isInteger(e.MID)
                    )
                      return "MID: integer expected";
                    if (
                      null != e.Num &&
                      e.hasOwnProperty("Num") &&
                      !f.isInteger(e.Num)
                    )
                      return "Num: integer expected";
                    if (null != e.MToken && e.hasOwnProperty("MToken")) {
                      var t = y.msg.MaterialToken.verify(e.MToken);
                      if (t) return "MToken." + t;
                    }
                    return null != e.C2 &&
                      e.hasOwnProperty("C2") &&
                      !f.isString(e.C2)
                      ? "C2: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.StoreBuy) return e;
                    var t = new y.msg.StoreBuy();
                    if (
                      (null != e.MID && (t.MID = 0 | e.MID),
                      null != e.Num && (t.Num = 0 | e.Num),
                      null != e.MToken)
                    ) {
                      if ("object" != typeof e.MToken)
                        throw TypeError(
                          ".msg.StoreBuy.MToken: object expected"
                        );
                      t.MToken = y.msg.MaterialToken.fromObject(e.MToken);
                    }
                    return null != e.C2 && (t.C2 = String(e.C2)), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.MID = 0),
                        (r.Num = 0),
                        (r.MToken = null),
                        (r.C2 = "")),
                      null != e.MID &&
                        e.hasOwnProperty("MID") &&
                        (r.MID = e.MID),
                      null != e.Num &&
                        e.hasOwnProperty("Num") &&
                        (r.Num = e.Num),
                      null != e.MToken &&
                        e.hasOwnProperty("MToken") &&
                        (r.MToken = y.msg.MaterialToken.toObject(e.MToken, t)),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.StoreSell = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.MID = 0),
                  (e.prototype.Num = 0),
                  (e.prototype.C2 = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.MID &&
                        Object.hasOwnProperty.call(e, "MID") &&
                        t.uint32(8).int32(e.MID),
                      null != e.Num &&
                        Object.hasOwnProperty.call(e, "Num") &&
                        t.uint32(16).int32(e.Num),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(26).string(e.C2),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.StoreSell();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.MID = e.int32();
                          break;
                        case 2:
                          n.Num = e.int32();
                          break;
                        case 3:
                          n.C2 = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.MID &&
                        e.hasOwnProperty("MID") &&
                        !f.isInteger(e.MID)
                      ? "MID: integer expected"
                      : null != e.Num &&
                        e.hasOwnProperty("Num") &&
                        !f.isInteger(e.Num)
                      ? "Num: integer expected"
                      : null != e.C2 &&
                        e.hasOwnProperty("C2") &&
                        !f.isString(e.C2)
                      ? "C2: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.StoreSell) return e;
                    var t = new y.msg.StoreSell();
                    return (
                      null != e.MID && (t.MID = 0 | e.MID),
                      null != e.Num && (t.Num = 0 | e.Num),
                      null != e.C2 && (t.C2 = String(e.C2)),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.MID = 0), (r.Num = 0), (r.C2 = "")),
                      null != e.MID &&
                        e.hasOwnProperty("MID") &&
                        (r.MID = e.MID),
                      null != e.Num &&
                        e.hasOwnProperty("Num") &&
                        (r.Num = e.Num),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameStart = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.HeroSN = 0),
                  (e.prototype.Chapter = 0),
                  (e.prototype.Skin = 0),
                  (e.prototype.Mode = 0),
                  (e.prototype.Field = null),
                  (e.prototype.DurCost = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.HeroSN &&
                        Object.hasOwnProperty.call(e, "HeroSN") &&
                        t.uint32(8).uint32(e.HeroSN),
                      null != e.Chapter &&
                        Object.hasOwnProperty.call(e, "Chapter") &&
                        t.uint32(16).int32(e.Chapter),
                      null != e.Skin &&
                        Object.hasOwnProperty.call(e, "Skin") &&
                        t.uint32(24).int32(e.Skin),
                      null != e.Mode &&
                        Object.hasOwnProperty.call(e, "Mode") &&
                        t.uint32(32).int32(e.Mode),
                      null != e.Field &&
                        Object.hasOwnProperty.call(e, "Field") &&
                        y.msg.BattleField.encode(
                          e.Field,
                          t.uint32(42).fork()
                        ).ldelim(),
                      null != e.DurCost &&
                        Object.hasOwnProperty.call(e, "DurCost") &&
                        t.uint32(48).int32(e.DurCost),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameStart();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.HeroSN = e.uint32();
                          break;
                        case 2:
                          n.Chapter = e.int32();
                          break;
                        case 3:
                          n.Skin = e.int32();
                          break;
                        case 4:
                          n.Mode = e.int32();
                          break;
                        case 5:
                          n.Field = y.msg.BattleField.decode(e, e.uint32());
                          break;
                        case 6:
                          n.DurCost = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.HeroSN &&
                      e.hasOwnProperty("HeroSN") &&
                      !f.isInteger(e.HeroSN)
                    )
                      return "HeroSN: integer expected";
                    if (
                      null != e.Chapter &&
                      e.hasOwnProperty("Chapter") &&
                      !f.isInteger(e.Chapter)
                    )
                      return "Chapter: integer expected";
                    if (
                      null != e.Skin &&
                      e.hasOwnProperty("Skin") &&
                      !f.isInteger(e.Skin)
                    )
                      return "Skin: integer expected";
                    if (
                      null != e.Mode &&
                      e.hasOwnProperty("Mode") &&
                      !f.isInteger(e.Mode)
                    )
                      return "Mode: integer expected";
                    if (null != e.Field && e.hasOwnProperty("Field")) {
                      var t = y.msg.BattleField.verify(e.Field);
                      if (t) return "Field." + t;
                    }
                    return null != e.DurCost &&
                      e.hasOwnProperty("DurCost") &&
                      !f.isInteger(e.DurCost)
                      ? "DurCost: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameStart) return e;
                    var t = new y.msg.GameStart();
                    if (
                      (null != e.HeroSN && (t.HeroSN = e.HeroSN >>> 0),
                      null != e.Chapter && (t.Chapter = 0 | e.Chapter),
                      null != e.Skin && (t.Skin = 0 | e.Skin),
                      null != e.Mode && (t.Mode = 0 | e.Mode),
                      null != e.Field)
                    ) {
                      if ("object" != typeof e.Field)
                        throw TypeError(
                          ".msg.GameStart.Field: object expected"
                        );
                      t.Field = y.msg.BattleField.fromObject(e.Field);
                    }
                    return null != e.DurCost && (t.DurCost = 0 | e.DurCost), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.HeroSN = 0),
                        (r.Chapter = 0),
                        (r.Skin = 0),
                        (r.Mode = 0),
                        (r.Field = null),
                        (r.DurCost = 0)),
                      null != e.HeroSN &&
                        e.hasOwnProperty("HeroSN") &&
                        (r.HeroSN = e.HeroSN),
                      null != e.Chapter &&
                        e.hasOwnProperty("Chapter") &&
                        (r.Chapter = e.Chapter),
                      null != e.Skin &&
                        e.hasOwnProperty("Skin") &&
                        (r.Skin = e.Skin),
                      null != e.Mode &&
                        e.hasOwnProperty("Mode") &&
                        (r.Mode = e.Mode),
                      null != e.Field &&
                        e.hasOwnProperty("Field") &&
                        (r.Field = y.msg.BattleField.toObject(e.Field, t)),
                      null != e.DurCost &&
                        e.hasOwnProperty("DurCost") &&
                        (r.DurCost = e.DurCost),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameLoaded = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return t || (t = p.create()), t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameLoaded();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      e.skipType(7 & o);
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    return e instanceof y.msg.GameLoaded
                      ? e
                      : new y.msg.GameLoaded();
                  }),
                  (e.toObject = function () {
                    return {};
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameDone = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.State = 0),
                  (e.prototype.StageRecord = 0),
                  (e.prototype.Shop = null),
                  (e.prototype.RealDrop = null),
                  (e.prototype.Update = null),
                  (e.prototype.FinalScore = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.State &&
                        Object.hasOwnProperty.call(e, "State") &&
                        t.uint32(8).int32(e.State),
                      null != e.StageRecord &&
                        Object.hasOwnProperty.call(e, "StageRecord") &&
                        t.uint32(16).int32(e.StageRecord),
                      null != e.Shop &&
                        Object.hasOwnProperty.call(e, "Shop") &&
                        y.msg.BattleShop.encode(
                          e.Shop,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.RealDrop &&
                        Object.hasOwnProperty.call(e, "RealDrop") &&
                        y.msg.DropInfo.encode(
                          e.RealDrop,
                          t.uint32(34).fork()
                        ).ldelim(),
                      null != e.Update &&
                        Object.hasOwnProperty.call(e, "Update") &&
                        y.msg.BattleUpdate.encode(
                          e.Update,
                          t.uint32(42).fork()
                        ).ldelim(),
                      null != e.FinalScore &&
                        Object.hasOwnProperty.call(e, "FinalScore") &&
                        t.uint32(48).int32(e.FinalScore),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameDone();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.State = e.int32();
                          break;
                        case 2:
                          n.StageRecord = e.int32();
                          break;
                        case 3:
                          n.Shop = y.msg.BattleShop.decode(e, e.uint32());
                          break;
                        case 4:
                          n.RealDrop = y.msg.DropInfo.decode(e, e.uint32());
                          break;
                        case 5:
                          n.Update = y.msg.BattleUpdate.decode(e, e.uint32());
                          break;
                        case 6:
                          n.FinalScore = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.State && e.hasOwnProperty("State"))
                      switch (e.State) {
                        default:
                          return "State: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                      }
                    return null != e.StageRecord &&
                      e.hasOwnProperty("StageRecord") &&
                      !f.isInteger(e.StageRecord)
                      ? "StageRecord: integer expected"
                      : null != e.Shop &&
                        e.hasOwnProperty("Shop") &&
                        (t = y.msg.BattleShop.verify(e.Shop))
                      ? "Shop." + t
                      : null != e.RealDrop &&
                        e.hasOwnProperty("RealDrop") &&
                        (t = y.msg.DropInfo.verify(e.RealDrop))
                      ? "RealDrop." + t
                      : null != e.Update &&
                        e.hasOwnProperty("Update") &&
                        (t = y.msg.BattleUpdate.verify(e.Update))
                      ? "Update." + t
                      : null != e.FinalScore &&
                        e.hasOwnProperty("FinalScore") &&
                        !f.isInteger(e.FinalScore)
                      ? "FinalScore: integer expected"
                      : null;
                    var t;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameDone) return e;
                    var t = new y.msg.GameDone();
                    switch (e.State) {
                      case "GS_Loading":
                      case 0:
                        t.State = 0;
                        break;
                      case "GS_Spawn":
                      case 1:
                        t.State = 1;
                        break;
                      case "GS_Shopping":
                      case 2:
                        t.State = 2;
                        break;
                      case "GS_Win":
                      case 3:
                        t.State = 3;
                        break;
                      case "GS_Lose":
                      case 4:
                        t.State = 4;
                        break;
                      case "GS_Quit":
                      case 5:
                        t.State = 5;
                        break;
                      case "GS_Exception":
                      case 6:
                        t.State = 6;
                    }
                    if (
                      (null != e.StageRecord &&
                        (t.StageRecord = 0 | e.StageRecord),
                      null != e.Shop)
                    ) {
                      if ("object" != typeof e.Shop)
                        throw TypeError(".msg.GameDone.Shop: object expected");
                      t.Shop = y.msg.BattleShop.fromObject(e.Shop);
                    }
                    if (null != e.RealDrop) {
                      if ("object" != typeof e.RealDrop)
                        throw TypeError(
                          ".msg.GameDone.RealDrop: object expected"
                        );
                      t.RealDrop = y.msg.DropInfo.fromObject(e.RealDrop);
                    }
                    if (null != e.Update) {
                      if ("object" != typeof e.Update)
                        throw TypeError(
                          ".msg.GameDone.Update: object expected"
                        );
                      t.Update = y.msg.BattleUpdate.fromObject(e.Update);
                    }
                    return (
                      null != e.FinalScore && (t.FinalScore = 0 | e.FinalScore),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.State = t.enums === String ? "GS_Loading" : 0),
                        (r.StageRecord = 0),
                        (r.Shop = null),
                        (r.RealDrop = null),
                        (r.Update = null),
                        (r.FinalScore = 0)),
                      null != e.State &&
                        e.hasOwnProperty("State") &&
                        (r.State =
                          t.enums === String
                            ? y.msg.GameState[e.State]
                            : e.State),
                      null != e.StageRecord &&
                        e.hasOwnProperty("StageRecord") &&
                        (r.StageRecord = e.StageRecord),
                      null != e.Shop &&
                        e.hasOwnProperty("Shop") &&
                        (r.Shop = y.msg.BattleShop.toObject(e.Shop, t)),
                      null != e.RealDrop &&
                        e.hasOwnProperty("RealDrop") &&
                        (r.RealDrop = y.msg.DropInfo.toObject(e.RealDrop, t)),
                      null != e.Update &&
                        e.hasOwnProperty("Update") &&
                        (r.Update = y.msg.BattleUpdate.toObject(e.Update, t)),
                      null != e.FinalScore &&
                        e.hasOwnProperty("FinalScore") &&
                        (r.FinalScore = e.FinalScore),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GamePause = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Pause = !1),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Pause &&
                        Object.hasOwnProperty.call(e, "Pause") &&
                        t.uint32(8).bool(e.Pause),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GamePause();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Pause = e.bool();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Pause &&
                        e.hasOwnProperty("Pause") &&
                        "boolean" != typeof e.Pause
                      ? "Pause: boolean expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GamePause) return e;
                    var t = new y.msg.GamePause();
                    return null != e.Pause && (t.Pause = Boolean(e.Pause)), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Pause = !1),
                      null != e.Pause &&
                        e.hasOwnProperty("Pause") &&
                        (r.Pause = e.Pause),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameShopOperate = (function () {
                function e(e) {
                  if (
                    ((this.OpVal = []),
                    (this.RefreshGold = []),
                    (this.Goods = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Op = 0),
                  (e.prototype.OpVal = f.emptyArray),
                  (e.prototype.AddGold = 0),
                  (e.prototype.ChestItem = 0),
                  (e.prototype.RefreshGold = f.emptyArray),
                  (e.prototype.Goods = f.emptyArray),
                  (e.prototype.Update = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Op &&
                        Object.hasOwnProperty.call(e, "Op") &&
                        t.uint32(8).int32(e.Op),
                      null != e.OpVal && e.OpVal.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.OpVal.length; ++r)
                        t.int32(e.OpVal[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.AddGold &&
                        Object.hasOwnProperty.call(e, "AddGold") &&
                        t.uint32(24).int32(e.AddGold),
                      null != e.ChestItem &&
                        Object.hasOwnProperty.call(e, "ChestItem") &&
                        t.uint32(32).int32(e.ChestItem),
                      null != e.RefreshGold && e.RefreshGold.length)
                    ) {
                      for (
                        t.uint32(42).fork(), r = 0;
                        r < e.RefreshGold.length;
                        ++r
                      )
                        t.int32(e.RefreshGold[r]);
                      t.ldelim();
                    }
                    if (null != e.Goods && e.Goods.length)
                      for (r = 0; r < e.Goods.length; ++r)
                        y.msg.BattleShopGoods.encode(
                          e.Goods[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return (
                      null != e.Update &&
                        Object.hasOwnProperty.call(e, "Update") &&
                        y.msg.BattleUpdate.encode(
                          e.Update,
                          t.uint32(58).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameShopOperate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Op = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.OpVal && n.OpVal.length) || (n.OpVal = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.OpVal.push(e.int32());
                          else n.OpVal.push(e.int32());
                          break;
                        case 3:
                          n.AddGold = e.int32();
                          break;
                        case 4:
                          n.ChestItem = e.int32();
                          break;
                        case 5:
                          if (
                            ((n.RefreshGold && n.RefreshGold.length) ||
                              (n.RefreshGold = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.RefreshGold.push(e.int32());
                          else n.RefreshGold.push(e.int32());
                          break;
                        case 6:
                          (n.Goods && n.Goods.length) || (n.Goods = []),
                            n.Goods.push(
                              y.msg.BattleShopGoods.decode(e, e.uint32())
                            );
                          break;
                        case 7:
                          n.Update = y.msg.BattleUpdate.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Op && e.hasOwnProperty("Op"))
                      switch (e.Op) {
                        default:
                          return "Op: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                        case 7:
                        case 8:
                      }
                    if (null != e.OpVal && e.hasOwnProperty("OpVal")) {
                      if (!Array.isArray(e.OpVal))
                        return "OpVal: array expected";
                      for (var t = 0; t < e.OpVal.length; ++t)
                        if (!f.isInteger(e.OpVal[t]))
                          return "OpVal: integer[] expected";
                    }
                    if (
                      null != e.AddGold &&
                      e.hasOwnProperty("AddGold") &&
                      !f.isInteger(e.AddGold)
                    )
                      return "AddGold: integer expected";
                    if (
                      null != e.ChestItem &&
                      e.hasOwnProperty("ChestItem") &&
                      !f.isInteger(e.ChestItem)
                    )
                      return "ChestItem: integer expected";
                    if (
                      null != e.RefreshGold &&
                      e.hasOwnProperty("RefreshGold")
                    ) {
                      if (!Array.isArray(e.RefreshGold))
                        return "RefreshGold: array expected";
                      for (t = 0; t < e.RefreshGold.length; ++t)
                        if (!f.isInteger(e.RefreshGold[t]))
                          return "RefreshGold: integer[] expected";
                    }
                    if (null != e.Goods && e.hasOwnProperty("Goods")) {
                      if (!Array.isArray(e.Goods))
                        return "Goods: array expected";
                      for (t = 0; t < e.Goods.length; ++t)
                        if ((r = y.msg.BattleShopGoods.verify(e.Goods[t])))
                          return "Goods." + r;
                    }
                    var r;
                    return null != e.Update &&
                      e.hasOwnProperty("Update") &&
                      (r = y.msg.BattleUpdate.verify(e.Update))
                      ? "Update." + r
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameShopOperate) return e;
                    var t = new y.msg.GameShopOperate();
                    switch (e.Op) {
                      case "GSO_ChestSell":
                      case 0:
                        t.Op = 0;
                        break;
                      case "GSO_ChestCollect":
                      case 1:
                        t.Op = 1;
                        break;
                      case "GSO_ItemSell":
                      case 2:
                        t.Op = 2;
                        break;
                      case "GSO_WeaponSell":
                      case 3:
                        t.Op = 3;
                        break;
                      case "GSO_WeaponMerge":
                      case 4:
                        t.Op = 4;
                        break;
                      case "GSO_GoodsRefresh":
                      case 5:
                        t.Op = 5;
                        break;
                      case "GSO_GoodsBuy":
                      case 6:
                        t.Op = 6;
                        break;
                      case "GSO_GoodsLock":
                      case 7:
                        t.Op = 7;
                        break;
                      case "GSO_GoodsClose":
                      case 8:
                        t.Op = 8;
                    }
                    if (e.OpVal) {
                      if (!Array.isArray(e.OpVal))
                        throw TypeError(
                          ".msg.GameShopOperate.OpVal: array expected"
                        );
                      t.OpVal = [];
                      for (var r = 0; r < e.OpVal.length; ++r)
                        t.OpVal[r] = 0 | e.OpVal[r];
                    }
                    if (
                      (null != e.AddGold && (t.AddGold = 0 | e.AddGold),
                      null != e.ChestItem && (t.ChestItem = 0 | e.ChestItem),
                      e.RefreshGold)
                    ) {
                      if (!Array.isArray(e.RefreshGold))
                        throw TypeError(
                          ".msg.GameShopOperate.RefreshGold: array expected"
                        );
                      for (
                        t.RefreshGold = [], r = 0;
                        r < e.RefreshGold.length;
                        ++r
                      )
                        t.RefreshGold[r] = 0 | e.RefreshGold[r];
                    }
                    if (e.Goods) {
                      if (!Array.isArray(e.Goods))
                        throw TypeError(
                          ".msg.GameShopOperate.Goods: array expected"
                        );
                      for (t.Goods = [], r = 0; r < e.Goods.length; ++r) {
                        if ("object" != typeof e.Goods[r])
                          throw TypeError(
                            ".msg.GameShopOperate.Goods: object expected"
                          );
                        t.Goods[r] = y.msg.BattleShopGoods.fromObject(
                          e.Goods[r]
                        );
                      }
                    }
                    if (null != e.Update) {
                      if ("object" != typeof e.Update)
                        throw TypeError(
                          ".msg.GameShopOperate.Update: object expected"
                        );
                      t.Update = y.msg.BattleUpdate.fromObject(e.Update);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.OpVal = []), (r.RefreshGold = []), (r.Goods = [])),
                      t.defaults &&
                        ((r.Op = t.enums === String ? "GSO_ChestSell" : 0),
                        (r.AddGold = 0),
                        (r.ChestItem = 0),
                        (r.Update = null)),
                      null != e.Op &&
                        e.hasOwnProperty("Op") &&
                        (r.Op =
                          t.enums === String
                            ? y.msg.GameShopOpType[e.Op]
                            : e.Op),
                      e.OpVal && e.OpVal.length)
                    ) {
                      r.OpVal = [];
                      for (var n = 0; n < e.OpVal.length; ++n)
                        r.OpVal[n] = e.OpVal[n];
                    }
                    if (
                      (null != e.AddGold &&
                        e.hasOwnProperty("AddGold") &&
                        (r.AddGold = e.AddGold),
                      null != e.ChestItem &&
                        e.hasOwnProperty("ChestItem") &&
                        (r.ChestItem = e.ChestItem),
                      e.RefreshGold && e.RefreshGold.length)
                    )
                      for (
                        r.RefreshGold = [], n = 0;
                        n < e.RefreshGold.length;
                        ++n
                      )
                        r.RefreshGold[n] = e.RefreshGold[n];
                    if (e.Goods && e.Goods.length)
                      for (r.Goods = [], n = 0; n < e.Goods.length; ++n)
                        r.Goods[n] = y.msg.BattleShopGoods.toObject(
                          e.Goods[n],
                          t
                        );
                    return (
                      null != e.Update &&
                        e.hasOwnProperty("Update") &&
                        (r.Update = y.msg.BattleUpdate.toObject(e.Update, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameQuit = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return t || (t = p.create()), t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameQuit();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      e.skipType(7 & o);
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    return e instanceof y.msg.GameQuit
                      ? e
                      : new y.msg.GameQuit();
                  }),
                  (e.toObject = function () {
                    return {};
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameSpawn = (function () {
                function e(e) {
                  if (((this.Spawn = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Spawn = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()), null != e.Spawn && e.Spawn.length)
                    )
                      for (var r = 0; r < e.Spawn.length; ++r)
                        y.msg.BattleSpawn.encode(
                          e.Spawn[r],
                          t.uint32(10).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameSpawn();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          (n.Spawn && n.Spawn.length) || (n.Spawn = []),
                            n.Spawn.push(
                              y.msg.BattleSpawn.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Spawn && e.hasOwnProperty("Spawn")) {
                      if (!Array.isArray(e.Spawn))
                        return "Spawn: array expected";
                      for (var t = 0; t < e.Spawn.length; ++t) {
                        var r = y.msg.BattleSpawn.verify(e.Spawn[t]);
                        if (r) return "Spawn." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameSpawn) return e;
                    var t = new y.msg.GameSpawn();
                    if (e.Spawn) {
                      if (!Array.isArray(e.Spawn))
                        throw TypeError(".msg.GameSpawn.Spawn: array expected");
                      t.Spawn = [];
                      for (var r = 0; r < e.Spawn.length; ++r) {
                        if ("object" != typeof e.Spawn[r])
                          throw TypeError(
                            ".msg.GameSpawn.Spawn: object expected"
                          );
                        t.Spawn[r] = y.msg.BattleSpawn.fromObject(e.Spawn[r]);
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.Spawn = []),
                      e.Spawn && e.Spawn.length)
                    ) {
                      r.Spawn = [];
                      for (var n = 0; n < e.Spawn.length; ++n)
                        r.Spawn[n] = y.msg.BattleSpawn.toObject(e.Spawn[n], t);
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameHeartbeat = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Cnt = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Cnt &&
                        Object.hasOwnProperty.call(e, "Cnt") &&
                        t.uint32(8).int32(e.Cnt),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameHeartbeat();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Cnt = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Cnt &&
                        e.hasOwnProperty("Cnt") &&
                        !f.isInteger(e.Cnt)
                      ? "Cnt: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameHeartbeat) return e;
                    var t = new y.msg.GameHeartbeat();
                    return null != e.Cnt && (t.Cnt = 0 | e.Cnt), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Cnt = 0),
                      null != e.Cnt &&
                        e.hasOwnProperty("Cnt") &&
                        (r.Cnt = e.Cnt),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameMonsterAtk = (function () {
                function e(e) {
                  if (((this.Atks = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Atks = f.emptyArray),
                  (e.prototype.DmgInfo = null),
                  (e.prototype.Heal = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()), null != e.Atks && e.Atks.length)
                    )
                      for (var r = 0; r < e.Atks.length; ++r)
                        y.msg.BattleMonsterAtk.encode(
                          e.Atks[r],
                          t.uint32(10).fork()
                        ).ldelim();
                    return (
                      null != e.DmgInfo &&
                        Object.hasOwnProperty.call(e, "DmgInfo") &&
                        y.msg.BattleDmg.encode(
                          e.DmgInfo,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.Heal &&
                        Object.hasOwnProperty.call(e, "Heal") &&
                        y.msg.BattleHeal.encode(
                          e.Heal,
                          t.uint32(34).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameMonsterAtk();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          (n.Atks && n.Atks.length) || (n.Atks = []),
                            n.Atks.push(
                              y.msg.BattleMonsterAtk.decode(e, e.uint32())
                            );
                          break;
                        case 3:
                          n.DmgInfo = y.msg.BattleDmg.decode(e, e.uint32());
                          break;
                        case 4:
                          n.Heal = y.msg.BattleHeal.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Atks && e.hasOwnProperty("Atks")) {
                      if (!Array.isArray(e.Atks)) return "Atks: array expected";
                      for (var t = 0; t < e.Atks.length; ++t)
                        if ((r = y.msg.BattleMonsterAtk.verify(e.Atks[t])))
                          return "Atks." + r;
                    }
                    var r;
                    return null != e.DmgInfo &&
                      e.hasOwnProperty("DmgInfo") &&
                      (r = y.msg.BattleDmg.verify(e.DmgInfo))
                      ? "DmgInfo." + r
                      : null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        (r = y.msg.BattleHeal.verify(e.Heal))
                      ? "Heal." + r
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameMonsterAtk) return e;
                    var t = new y.msg.GameMonsterAtk();
                    if (e.Atks) {
                      if (!Array.isArray(e.Atks))
                        throw TypeError(
                          ".msg.GameMonsterAtk.Atks: array expected"
                        );
                      t.Atks = [];
                      for (var r = 0; r < e.Atks.length; ++r) {
                        if ("object" != typeof e.Atks[r])
                          throw TypeError(
                            ".msg.GameMonsterAtk.Atks: object expected"
                          );
                        t.Atks[r] = y.msg.BattleMonsterAtk.fromObject(
                          e.Atks[r]
                        );
                      }
                    }
                    if (null != e.DmgInfo) {
                      if ("object" != typeof e.DmgInfo)
                        throw TypeError(
                          ".msg.GameMonsterAtk.DmgInfo: object expected"
                        );
                      t.DmgInfo = y.msg.BattleDmg.fromObject(e.DmgInfo);
                    }
                    if (null != e.Heal) {
                      if ("object" != typeof e.Heal)
                        throw TypeError(
                          ".msg.GameMonsterAtk.Heal: object expected"
                        );
                      t.Heal = y.msg.BattleHeal.fromObject(e.Heal);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.Atks = []),
                      t.defaults && ((r.DmgInfo = null), (r.Heal = null)),
                      e.Atks && e.Atks.length)
                    ) {
                      r.Atks = [];
                      for (var n = 0; n < e.Atks.length; ++n)
                        r.Atks[n] = y.msg.BattleMonsterAtk.toObject(
                          e.Atks[n],
                          t
                        );
                    }
                    return (
                      null != e.DmgInfo &&
                        e.hasOwnProperty("DmgInfo") &&
                        (r.DmgInfo = y.msg.BattleDmg.toObject(e.DmgInfo, t)),
                      null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        (r.Heal = y.msg.BattleHeal.toObject(e.Heal, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameCharAtk = (function () {
                function e(e) {
                  if (
                    ((this.AOInfo = []),
                    (this.CltDmg = []),
                    (this.Heal = []),
                    (this.DropItems = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.AOT = 0),
                  (e.prototype.AOInfo = f.emptyArray),
                  (e.prototype.CltDmg = f.emptyArray),
                  (e.prototype.Heal = f.emptyArray),
                  (e.prototype.DropItems = f.emptyArray),
                  (e.prototype.AddScore = 0),
                  (e.prototype.DChest = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.AOT &&
                        Object.hasOwnProperty.call(e, "AOT") &&
                        t.uint32(8).int32(e.AOT),
                      null != e.AOInfo && e.AOInfo.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.AOInfo.length; ++r)
                        t.int32(e.AOInfo[r]);
                      t.ldelim();
                    }
                    if (null != e.CltDmg && e.CltDmg.length)
                      for (r = 0; r < e.CltDmg.length; ++r)
                        y.msg.BattleDmg.encode(
                          e.CltDmg[r],
                          t.uint32(34).fork()
                        ).ldelim();
                    if (null != e.Heal && e.Heal.length)
                      for (r = 0; r < e.Heal.length; ++r)
                        y.msg.BattleHeal.encode(
                          e.Heal[r],
                          t.uint32(42).fork()
                        ).ldelim();
                    if (null != e.DropItems && e.DropItems.length)
                      for (r = 0; r < e.DropItems.length; ++r)
                        y.msg.BattleTileItem.encode(
                          e.DropItems[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return (
                      null != e.AddScore &&
                        Object.hasOwnProperty.call(e, "AddScore") &&
                        t.uint32(56).int32(e.AddScore),
                      null != e.DChest &&
                        Object.hasOwnProperty.call(e, "DChest") &&
                        y.msg.DropChest.encode(
                          e.DChest,
                          t.uint32(66).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameCharAtk();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.AOT = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.AOInfo && n.AOInfo.length) || (n.AOInfo = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.AOInfo.push(e.int32());
                          else n.AOInfo.push(e.int32());
                          break;
                        case 4:
                          (n.CltDmg && n.CltDmg.length) || (n.CltDmg = []),
                            n.CltDmg.push(
                              y.msg.BattleDmg.decode(e, e.uint32())
                            );
                          break;
                        case 5:
                          (n.Heal && n.Heal.length) || (n.Heal = []),
                            n.Heal.push(y.msg.BattleHeal.decode(e, e.uint32()));
                          break;
                        case 6:
                          (n.DropItems && n.DropItems.length) ||
                            (n.DropItems = []),
                            n.DropItems.push(
                              y.msg.BattleTileItem.decode(e, e.uint32())
                            );
                          break;
                        case 7:
                          n.AddScore = e.int32();
                          break;
                        case 8:
                          n.DChest = y.msg.DropChest.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.AOT && e.hasOwnProperty("AOT"))
                      switch (e.AOT) {
                        default:
                          return "AOT: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                      }
                    if (null != e.AOInfo && e.hasOwnProperty("AOInfo")) {
                      if (!Array.isArray(e.AOInfo))
                        return "AOInfo: array expected";
                      for (var t = 0; t < e.AOInfo.length; ++t)
                        if (!f.isInteger(e.AOInfo[t]))
                          return "AOInfo: integer[] expected";
                    }
                    if (null != e.CltDmg && e.hasOwnProperty("CltDmg")) {
                      if (!Array.isArray(e.CltDmg))
                        return "CltDmg: array expected";
                      for (t = 0; t < e.CltDmg.length; ++t)
                        if ((r = y.msg.BattleDmg.verify(e.CltDmg[t])))
                          return "CltDmg." + r;
                    }
                    if (null != e.Heal && e.hasOwnProperty("Heal")) {
                      if (!Array.isArray(e.Heal)) return "Heal: array expected";
                      for (t = 0; t < e.Heal.length; ++t)
                        if ((r = y.msg.BattleHeal.verify(e.Heal[t])))
                          return "Heal." + r;
                    }
                    if (null != e.DropItems && e.hasOwnProperty("DropItems")) {
                      if (!Array.isArray(e.DropItems))
                        return "DropItems: array expected";
                      for (t = 0; t < e.DropItems.length; ++t)
                        if ((r = y.msg.BattleTileItem.verify(e.DropItems[t])))
                          return "DropItems." + r;
                    }
                    return null != e.AddScore &&
                      e.hasOwnProperty("AddScore") &&
                      !f.isInteger(e.AddScore)
                      ? "AddScore: integer expected"
                      : null != e.DChest &&
                        e.hasOwnProperty("DChest") &&
                        (r = y.msg.DropChest.verify(e.DChest))
                      ? "DChest." + r
                      : null;
                    var r;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameCharAtk) return e;
                    var t = new y.msg.GameCharAtk();
                    switch (e.AOT) {
                      case "AOT_Weapon":
                      case 0:
                        t.AOT = 0;
                        break;
                      case "AOT_Tower":
                      case 1:
                        t.AOT = 1;
                        break;
                      case "AOT_Talent":
                      case 2:
                        t.AOT = 2;
                        break;
                      case "AOT_Monster":
                      case 3:
                        t.AOT = 3;
                        break;
                      case "AOT_Item":
                      case 4:
                        t.AOT = 4;
                    }
                    if (e.AOInfo) {
                      if (!Array.isArray(e.AOInfo))
                        throw TypeError(
                          ".msg.GameCharAtk.AOInfo: array expected"
                        );
                      t.AOInfo = [];
                      for (var r = 0; r < e.AOInfo.length; ++r)
                        t.AOInfo[r] = 0 | e.AOInfo[r];
                    }
                    if (e.CltDmg) {
                      if (!Array.isArray(e.CltDmg))
                        throw TypeError(
                          ".msg.GameCharAtk.CltDmg: array expected"
                        );
                      for (t.CltDmg = [], r = 0; r < e.CltDmg.length; ++r) {
                        if ("object" != typeof e.CltDmg[r])
                          throw TypeError(
                            ".msg.GameCharAtk.CltDmg: object expected"
                          );
                        t.CltDmg[r] = y.msg.BattleDmg.fromObject(e.CltDmg[r]);
                      }
                    }
                    if (e.Heal) {
                      if (!Array.isArray(e.Heal))
                        throw TypeError(
                          ".msg.GameCharAtk.Heal: array expected"
                        );
                      for (t.Heal = [], r = 0; r < e.Heal.length; ++r) {
                        if ("object" != typeof e.Heal[r])
                          throw TypeError(
                            ".msg.GameCharAtk.Heal: object expected"
                          );
                        t.Heal[r] = y.msg.BattleHeal.fromObject(e.Heal[r]);
                      }
                    }
                    if (e.DropItems) {
                      if (!Array.isArray(e.DropItems))
                        throw TypeError(
                          ".msg.GameCharAtk.DropItems: array expected"
                        );
                      for (
                        t.DropItems = [], r = 0;
                        r < e.DropItems.length;
                        ++r
                      ) {
                        if ("object" != typeof e.DropItems[r])
                          throw TypeError(
                            ".msg.GameCharAtk.DropItems: object expected"
                          );
                        t.DropItems[r] = y.msg.BattleTileItem.fromObject(
                          e.DropItems[r]
                        );
                      }
                    }
                    if (
                      (null != e.AddScore && (t.AddScore = 0 | e.AddScore),
                      null != e.DChest)
                    ) {
                      if ("object" != typeof e.DChest)
                        throw TypeError(
                          ".msg.GameCharAtk.DChest: object expected"
                        );
                      t.DChest = y.msg.DropChest.fromObject(e.DChest);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.AOInfo = []),
                        (r.CltDmg = []),
                        (r.Heal = []),
                        (r.DropItems = [])),
                      t.defaults &&
                        ((r.AOT = t.enums === String ? "AOT_Weapon" : 0),
                        (r.AddScore = 0),
                        (r.DChest = null)),
                      null != e.AOT &&
                        e.hasOwnProperty("AOT") &&
                        (r.AOT =
                          t.enums === String ? y.msg.AtkObjType[e.AOT] : e.AOT),
                      e.AOInfo && e.AOInfo.length)
                    ) {
                      r.AOInfo = [];
                      for (var n = 0; n < e.AOInfo.length; ++n)
                        r.AOInfo[n] = e.AOInfo[n];
                    }
                    if (e.CltDmg && e.CltDmg.length)
                      for (r.CltDmg = [], n = 0; n < e.CltDmg.length; ++n)
                        r.CltDmg[n] = y.msg.BattleDmg.toObject(e.CltDmg[n], t);
                    if (e.Heal && e.Heal.length)
                      for (r.Heal = [], n = 0; n < e.Heal.length; ++n)
                        r.Heal[n] = y.msg.BattleHeal.toObject(e.Heal[n], t);
                    if (e.DropItems && e.DropItems.length)
                      for (r.DropItems = [], n = 0; n < e.DropItems.length; ++n)
                        r.DropItems[n] = y.msg.BattleTileItem.toObject(
                          e.DropItems[n],
                          t
                        );
                    return (
                      null != e.AddScore &&
                        e.hasOwnProperty("AddScore") &&
                        (r.AddScore = e.AddScore),
                      null != e.DChest &&
                        e.hasOwnProperty("DChest") &&
                        (r.DChest = y.msg.DropChest.toObject(e.DChest, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameDmgEx = (function () {
                function e(e) {
                  if (((this.CltDmg = []), (this.DropItems = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.CltDmg = f.emptyArray),
                  (e.prototype.DropItems = f.emptyArray),
                  (e.prototype.AddScore = 0),
                  (e.prototype.DChest = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.CltDmg && e.CltDmg.length)
                    )
                      for (var r = 0; r < e.CltDmg.length; ++r)
                        y.msg.BattleDmgEx.encode(
                          e.CltDmg[r],
                          t.uint32(10).fork()
                        ).ldelim();
                    if (null != e.DropItems && e.DropItems.length)
                      for (r = 0; r < e.DropItems.length; ++r)
                        y.msg.BattleTileItem.encode(
                          e.DropItems[r],
                          t.uint32(18).fork()
                        ).ldelim();
                    return (
                      null != e.AddScore &&
                        Object.hasOwnProperty.call(e, "AddScore") &&
                        t.uint32(24).int32(e.AddScore),
                      null != e.DChest &&
                        Object.hasOwnProperty.call(e, "DChest") &&
                        y.msg.DropChest.encode(
                          e.DChest,
                          t.uint32(34).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameDmgEx();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          (n.CltDmg && n.CltDmg.length) || (n.CltDmg = []),
                            n.CltDmg.push(
                              y.msg.BattleDmgEx.decode(e, e.uint32())
                            );
                          break;
                        case 2:
                          (n.DropItems && n.DropItems.length) ||
                            (n.DropItems = []),
                            n.DropItems.push(
                              y.msg.BattleTileItem.decode(e, e.uint32())
                            );
                          break;
                        case 3:
                          n.AddScore = e.int32();
                          break;
                        case 4:
                          n.DChest = y.msg.DropChest.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.CltDmg && e.hasOwnProperty("CltDmg")) {
                      if (!Array.isArray(e.CltDmg))
                        return "CltDmg: array expected";
                      for (var t = 0; t < e.CltDmg.length; ++t)
                        if ((r = y.msg.BattleDmgEx.verify(e.CltDmg[t])))
                          return "CltDmg." + r;
                    }
                    if (null != e.DropItems && e.hasOwnProperty("DropItems")) {
                      if (!Array.isArray(e.DropItems))
                        return "DropItems: array expected";
                      for (t = 0; t < e.DropItems.length; ++t)
                        if ((r = y.msg.BattleTileItem.verify(e.DropItems[t])))
                          return "DropItems." + r;
                    }
                    return null != e.AddScore &&
                      e.hasOwnProperty("AddScore") &&
                      !f.isInteger(e.AddScore)
                      ? "AddScore: integer expected"
                      : null != e.DChest &&
                        e.hasOwnProperty("DChest") &&
                        (r = y.msg.DropChest.verify(e.DChest))
                      ? "DChest." + r
                      : null;
                    var r;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameDmgEx) return e;
                    var t = new y.msg.GameDmgEx();
                    if (e.CltDmg) {
                      if (!Array.isArray(e.CltDmg))
                        throw TypeError(
                          ".msg.GameDmgEx.CltDmg: array expected"
                        );
                      t.CltDmg = [];
                      for (var r = 0; r < e.CltDmg.length; ++r) {
                        if ("object" != typeof e.CltDmg[r])
                          throw TypeError(
                            ".msg.GameDmgEx.CltDmg: object expected"
                          );
                        t.CltDmg[r] = y.msg.BattleDmgEx.fromObject(e.CltDmg[r]);
                      }
                    }
                    if (e.DropItems) {
                      if (!Array.isArray(e.DropItems))
                        throw TypeError(
                          ".msg.GameDmgEx.DropItems: array expected"
                        );
                      for (
                        t.DropItems = [], r = 0;
                        r < e.DropItems.length;
                        ++r
                      ) {
                        if ("object" != typeof e.DropItems[r])
                          throw TypeError(
                            ".msg.GameDmgEx.DropItems: object expected"
                          );
                        t.DropItems[r] = y.msg.BattleTileItem.fromObject(
                          e.DropItems[r]
                        );
                      }
                    }
                    if (
                      (null != e.AddScore && (t.AddScore = 0 | e.AddScore),
                      null != e.DChest)
                    ) {
                      if ("object" != typeof e.DChest)
                        throw TypeError(
                          ".msg.GameDmgEx.DChest: object expected"
                        );
                      t.DChest = y.msg.DropChest.fromObject(e.DChest);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.CltDmg = []), (r.DropItems = [])),
                      t.defaults && ((r.AddScore = 0), (r.DChest = null)),
                      e.CltDmg && e.CltDmg.length)
                    ) {
                      r.CltDmg = [];
                      for (var n = 0; n < e.CltDmg.length; ++n)
                        r.CltDmg[n] = y.msg.BattleDmgEx.toObject(
                          e.CltDmg[n],
                          t
                        );
                    }
                    if (e.DropItems && e.DropItems.length)
                      for (r.DropItems = [], n = 0; n < e.DropItems.length; ++n)
                        r.DropItems[n] = y.msg.BattleTileItem.toObject(
                          e.DropItems[n],
                          t
                        );
                    return (
                      null != e.AddScore &&
                        e.hasOwnProperty("AddScore") &&
                        (r.AddScore = e.AddScore),
                      null != e.DChest &&
                        e.hasOwnProperty("DChest") &&
                        (r.DChest = y.msg.DropChest.toObject(e.DChest, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GamePick = (function () {
                function e(e) {
                  if (((this.Pos = []), (this.HPAttr = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.Pos = f.emptyArray),
                  (e.prototype.Heal = null),
                  (e.prototype.HPAttr = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.Pos && e.Pos.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.Pos.length; ++r) t.int32(e.Pos[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.Heal &&
                        Object.hasOwnProperty.call(e, "Heal") &&
                        y.msg.BattleHeal.encode(
                          e.Heal,
                          t.uint32(26).fork()
                        ).ldelim(),
                      null != e.HPAttr && e.HPAttr.length)
                    ) {
                      for (t.uint32(34).fork(), r = 0; r < e.HPAttr.length; ++r)
                        t.int32(e.HPAttr[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GamePick();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.Pos && n.Pos.length) || (n.Pos = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.Pos.push(e.int32());
                          else n.Pos.push(e.int32());
                          break;
                        case 3:
                          n.Heal = y.msg.BattleHeal.decode(e, e.uint32());
                          break;
                        case 4:
                          if (
                            ((n.HPAttr && n.HPAttr.length) || (n.HPAttr = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.HPAttr.push(e.int32());
                          else n.HPAttr.push(e.int32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Type &&
                      e.hasOwnProperty("Type") &&
                      !f.isInteger(e.Type)
                    )
                      return "Type: integer expected";
                    if (null != e.Pos && e.hasOwnProperty("Pos")) {
                      if (!Array.isArray(e.Pos)) return "Pos: array expected";
                      for (var t = 0; t < e.Pos.length; ++t)
                        if (!f.isInteger(e.Pos[t]))
                          return "Pos: integer[] expected";
                    }
                    if (null != e.Heal && e.hasOwnProperty("Heal")) {
                      var r = y.msg.BattleHeal.verify(e.Heal);
                      if (r) return "Heal." + r;
                    }
                    if (null != e.HPAttr && e.hasOwnProperty("HPAttr")) {
                      if (!Array.isArray(e.HPAttr))
                        return "HPAttr: array expected";
                      for (t = 0; t < e.HPAttr.length; ++t)
                        if (!f.isInteger(e.HPAttr[t]))
                          return "HPAttr: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GamePick) return e;
                    var t = new y.msg.GamePick();
                    if ((null != e.Type && (t.Type = 0 | e.Type), e.Pos)) {
                      if (!Array.isArray(e.Pos))
                        throw TypeError(".msg.GamePick.Pos: array expected");
                      t.Pos = [];
                      for (var r = 0; r < e.Pos.length; ++r)
                        t.Pos[r] = 0 | e.Pos[r];
                    }
                    if (null != e.Heal) {
                      if ("object" != typeof e.Heal)
                        throw TypeError(".msg.GamePick.Heal: object expected");
                      t.Heal = y.msg.BattleHeal.fromObject(e.Heal);
                    }
                    if (e.HPAttr) {
                      if (!Array.isArray(e.HPAttr))
                        throw TypeError(".msg.GamePick.HPAttr: array expected");
                      for (t.HPAttr = [], r = 0; r < e.HPAttr.length; ++r)
                        t.HPAttr[r] = 0 | e.HPAttr[r];
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.Pos = []), (r.HPAttr = [])),
                      t.defaults && ((r.Type = 0), (r.Heal = null)),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      e.Pos && e.Pos.length)
                    ) {
                      r.Pos = [];
                      for (var n = 0; n < e.Pos.length; ++n)
                        r.Pos[n] = e.Pos[n];
                    }
                    if (
                      (null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        (r.Heal = y.msg.BattleHeal.toObject(e.Heal, t)),
                      e.HPAttr && e.HPAttr.length)
                    )
                      for (r.HPAttr = [], n = 0; n < e.HPAttr.length; ++n)
                        r.HPAttr[n] = e.HPAttr[n];
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameWeaponUpdate = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.WSN = 0),
                  (e.prototype.WUpdate = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.WSN &&
                        Object.hasOwnProperty.call(e, "WSN") &&
                        t.uint32(8).int32(e.WSN),
                      null != e.WUpdate &&
                        Object.hasOwnProperty.call(e, "WUpdate") &&
                        y.msg.BattleWeaponUpdate.encode(
                          e.WUpdate,
                          t.uint32(18).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameWeaponUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.WSN = e.int32();
                          break;
                        case 2:
                          n.WUpdate = y.msg.BattleWeaponUpdate.decode(
                            e,
                            e.uint32()
                          );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.WSN &&
                      e.hasOwnProperty("WSN") &&
                      !f.isInteger(e.WSN)
                    )
                      return "WSN: integer expected";
                    if (null != e.WUpdate && e.hasOwnProperty("WUpdate")) {
                      var t = y.msg.BattleWeaponUpdate.verify(e.WUpdate);
                      if (t) return "WUpdate." + t;
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameWeaponUpdate) return e;
                    var t = new y.msg.GameWeaponUpdate();
                    if (
                      (null != e.WSN && (t.WSN = 0 | e.WSN), null != e.WUpdate)
                    ) {
                      if ("object" != typeof e.WUpdate)
                        throw TypeError(
                          ".msg.GameWeaponUpdate.WUpdate: object expected"
                        );
                      t.WUpdate = y.msg.BattleWeaponUpdate.fromObject(
                        e.WUpdate
                      );
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.WSN = 0), (r.WUpdate = null)),
                      null != e.WSN &&
                        e.hasOwnProperty("WSN") &&
                        (r.WSN = e.WSN),
                      null != e.WUpdate &&
                        e.hasOwnProperty("WUpdate") &&
                        (r.WUpdate = y.msg.BattleWeaponUpdate.toObject(
                          e.WUpdate,
                          t
                        )),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.GameLifeRecover = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Heal = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Heal &&
                        Object.hasOwnProperty.call(e, "Heal") &&
                        y.msg.BattleHeal.encode(
                          e.Heal,
                          t.uint32(10).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.GameLifeRecover();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Heal = y.msg.BattleHeal.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Heal && e.hasOwnProperty("Heal")) {
                      var t = y.msg.BattleHeal.verify(e.Heal);
                      if (t) return "Heal." + t;
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.GameLifeRecover) return e;
                    var t = new y.msg.GameLifeRecover();
                    if (null != e.Heal) {
                      if ("object" != typeof e.Heal)
                        throw TypeError(
                          ".msg.GameLifeRecover.Heal: object expected"
                        );
                      t.Heal = y.msg.BattleHeal.fromObject(e.Heal);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && (r.Heal = null),
                      null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        (r.Heal = y.msg.BattleHeal.toObject(e.Heal, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.LogType =
                ((a = {}),
                ((l = Object.create(a))[(a[0] = "LT_Nothing")] = 0),
                (l[(a[1] = "LT_EquipmentMerge")] = 1),
                (l[(a[2] = "LT_EquipmentRepair")] = 2),
                (l[(a[32] = "LT_BattleLaunch")] = 32),
                (l[(a[33] = "LT_BattleFinish")] = 33),
                (l[(a[64] = "LT_BuyMaterial")] = 64),
                (l[(a[65] = "LT_SellMaterial")] = 65),
                (l[(a[256] = "LT_GM_Hero")] = 256),
                (l[(a[257] = "LT_GM_Equipment")] = 257),
                (l[(a[258] = "LT_GM_Material")] = 258),
                l)),
              (s.ResponseCode = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "RC_Success")] = 0),
                  (t[(e[1] = "RC_UnknowErr")] = 1),
                  (t[(e[2] = "RC_Sign")] = 2),
                  (t[(e[3] = "RC_ParamLack")] = 3),
                  (t[(e[4] = "RC_DBErr")] = 4),
                  (t[(e[5] = "RC_RedisErr")] = 5),
                  (t[(e[6] = "RC_SessionErr")] = 6),
                  (t[(e[7] = "RC_ParamErr")] = 7),
                  (t[(e[8] = "RC_NoRight")] = 8),
                  (t[(e[9] = "RC_LogicErr")] = 9),
                  (t[(e[10] = "RC_SveTips")] = 10),
                  t
                );
              })()),
              (s.BattleVer = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "BV_Ver0")] = 0), (t[(e[1] = "BV_Cur")] = 1), t
                );
              })()),
              (s.GameState = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "GS_Loading")] = 0),
                  (t[(e[1] = "GS_Spawn")] = 1),
                  (t[(e[2] = "GS_Shopping")] = 2),
                  (t[(e[3] = "GS_Win")] = 3),
                  (t[(e[4] = "GS_Lose")] = 4),
                  (t[(e[5] = "GS_Quit")] = 5),
                  (t[(e[6] = "GS_Exception")] = 6),
                  t
                );
              })()),
              (s.ClassType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "CT_Nothing")] = 0),
                  (t[(e[1] = "CT_Human")] = 1),
                  (t[(e[2] = "CT_Orc")] = 2),
                  (t[(e[3] = "CT_Elf")] = 3),
                  (t[(e[4] = "CT_Undead")] = 4),
                  (t[(e[5] = "CT_Cyborg")] = 5),
                  (t[(e[6] = "CT_Firefolk")] = 6),
                  (t[(e[7] = "CT_Lightling")] = 7),
                  (t[(e[8] = "CT_Turing")] = 8),
                  (t[(e[9] = "CT_Spirit")] = 9),
                  (t[(e[10] = "CT_NoClass")] = 10),
                  t
                );
              })()),
              (s.AttrType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "AT_CurLife")] = 0),
                  (t[(e[1] = "AT_MaxLife")] = 1),
                  (t[(e[2] = "AT_Dmg")] = 2),
                  (t[(e[3] = "AT_Defense")] = 3),
                  (t[(e[4] = "AT_BaseMoveSpeed")] = 4),
                  (t[(e[5] = "AT_AtkRange")] = 5),
                  (t[(e[6] = "AT_AtkLength")] = 6),
                  (t[(e[7] = "AT_KnockbackReduce")] = 7),
                  (t[(e[8] = "AT_IncreaseChargeSpeed")] = 8),
                  (t[(e[9] = "AT_CollisionCD")] = 9),
                  (t[(e[10] = "AT_AtkCD")] = 10),
                  (t[(e[11] = "AT_RealMoveSpeed")] = 11),
                  (t[(e[12] = "AT_CollisionRadius")] = 12),
                  (t[(e[13] = "AT_WeaponMax")] = 13),
                  (t[(e[14] = "AT_LifeRecover")] = 14),
                  (t[(e[15] = "AT_IncreaseDot")] = 15),
                  (t[(e[16] = "AT_AtkSpeed")] = 16),
                  (t[(e[17] = "AT_IncreaseMoveSpeed")] = 17),
                  (t[(e[18] = "AT_CriticalRate")] = 18),
                  (t[(e[19] = "AT_CriticalDmg")] = 19),
                  (t[(e[20] = "AT_Dodge")] = 20),
                  (t[(e[21] = "AT_Enr")] = 21),
                  (t[(e[22] = "AT_Luck")] = 22),
                  (t[(e[23] = "AT_Knockback")] = 23),
                  (t[(e[24] = "AT_EnhanceHeal")] = 24),
                  (t[(e[25] = "AT_IncreaseDmg")] = 25),
                  (t[(e[26] = "AT_PickRange")] = 26),
                  (t[(e[28] = "AT_Max")] = 28),
                  t
                );
              })()),
              (s.WeaponAttrType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "WAT_Dmg")] = 0),
                  (t[(e[1] = "WAT_DmgCritical")] = 1),
                  (t[(e[2] = "WAT_AtkRange")] = 2),
                  (t[(e[3] = "WAT_AtkLength")] = 3),
                  (t[(e[4] = "WAT_Knockback")] = 4),
                  (t[(e[5] = "WAT_DotDmg")] = 5),
                  t
                );
              })()),
              (s.AtkObjType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "AOT_Weapon")] = 0),
                  (t[(e[1] = "AOT_Tower")] = 1),
                  (t[(e[2] = "AOT_Talent")] = 2),
                  (t[(e[3] = "AOT_Monster")] = 3),
                  (t[(e[4] = "AOT_Item")] = 4),
                  t
                );
              })()),
              (s.GameShopOpType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "GSO_ChestSell")] = 0),
                  (t[(e[1] = "GSO_ChestCollect")] = 1),
                  (t[(e[2] = "GSO_ItemSell")] = 2),
                  (t[(e[3] = "GSO_WeaponSell")] = 3),
                  (t[(e[4] = "GSO_WeaponMerge")] = 4),
                  (t[(e[5] = "GSO_GoodsRefresh")] = 5),
                  (t[(e[6] = "GSO_GoodsBuy")] = 6),
                  (t[(e[7] = "GSO_GoodsLock")] = 7),
                  (t[(e[8] = "GSO_GoodsClose")] = 8),
                  t
                );
              })()),
              (s.BattleTileItemType = (function () {
                var e = {},
                  t = Object.create(e);
                return (
                  (t[(e[0] = "BTI_Coin")] = 0),
                  (t[(e[1] = "BTI_Chest")] = 1),
                  (t[(e[2] = "BTI_Potion")] = 2),
                  t
                );
              })()),
              (s.PlayerData = (function () {
                function e(e) {
                  if (
                    ((this.HTokens = []),
                    (this.ETokens = []),
                    (this.StageRecord = []),
                    (this.MTokens = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.User = null),
                  (e.prototype.HTokens = f.emptyArray),
                  (e.prototype.ETokens = f.emptyArray),
                  (e.prototype.Third = null),
                  (e.prototype.StageRecord = f.emptyArray),
                  (e.prototype.MTokens = f.emptyArray),
                  (e.prototype.BField = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.User &&
                        Object.hasOwnProperty.call(e, "User") &&
                        y.msg.UserPart.encode(
                          e.User,
                          t.uint32(10).fork()
                        ).ldelim(),
                      null != e.HTokens && e.HTokens.length)
                    )
                      for (var r = 0; r < e.HTokens.length; ++r)
                        y.msg.HeroToken.encode(
                          e.HTokens[r],
                          t.uint32(18).fork()
                        ).ldelim();
                    if (null != e.ETokens && e.ETokens.length)
                      for (r = 0; r < e.ETokens.length; ++r)
                        y.msg.EquipmentToken.encode(
                          e.ETokens[r],
                          t.uint32(26).fork()
                        ).ldelim();
                    if (
                      (null != e.Third &&
                        Object.hasOwnProperty.call(e, "Third") &&
                        y.msg.ThirdPartyInfo.encode(
                          e.Third,
                          t.uint32(34).fork()
                        ).ldelim(),
                      null != e.StageRecord && e.StageRecord.length)
                    ) {
                      for (
                        t.uint32(42).fork(), r = 0;
                        r < e.StageRecord.length;
                        ++r
                      )
                        t.int32(e.StageRecord[r]);
                      t.ldelim();
                    }
                    if (null != e.MTokens && e.MTokens.length)
                      for (r = 0; r < e.MTokens.length; ++r)
                        y.msg.MaterialToken.encode(
                          e.MTokens[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return (
                      null != e.BField &&
                        Object.hasOwnProperty.call(e, "BField") &&
                        y.msg.BattleField.encode(
                          e.BField,
                          t.uint32(58).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.PlayerData();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.User = y.msg.UserPart.decode(e, e.uint32());
                          break;
                        case 2:
                          (n.HTokens && n.HTokens.length) || (n.HTokens = []),
                            n.HTokens.push(
                              y.msg.HeroToken.decode(e, e.uint32())
                            );
                          break;
                        case 3:
                          (n.ETokens && n.ETokens.length) || (n.ETokens = []),
                            n.ETokens.push(
                              y.msg.EquipmentToken.decode(e, e.uint32())
                            );
                          break;
                        case 4:
                          n.Third = y.msg.ThirdPartyInfo.decode(e, e.uint32());
                          break;
                        case 5:
                          if (
                            ((n.StageRecord && n.StageRecord.length) ||
                              (n.StageRecord = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.StageRecord.push(e.int32());
                          else n.StageRecord.push(e.int32());
                          break;
                        case 6:
                          (n.MTokens && n.MTokens.length) || (n.MTokens = []),
                            n.MTokens.push(
                              y.msg.MaterialToken.decode(e, e.uint32())
                            );
                          break;
                        case 7:
                          n.BField = y.msg.BattleField.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.User &&
                      e.hasOwnProperty("User") &&
                      (r = y.msg.UserPart.verify(e.User))
                    )
                      return "User." + r;
                    if (null != e.HTokens && e.hasOwnProperty("HTokens")) {
                      if (!Array.isArray(e.HTokens))
                        return "HTokens: array expected";
                      for (var t = 0; t < e.HTokens.length; ++t)
                        if ((r = y.msg.HeroToken.verify(e.HTokens[t])))
                          return "HTokens." + r;
                    }
                    if (null != e.ETokens && e.hasOwnProperty("ETokens")) {
                      if (!Array.isArray(e.ETokens))
                        return "ETokens: array expected";
                      for (t = 0; t < e.ETokens.length; ++t)
                        if ((r = y.msg.EquipmentToken.verify(e.ETokens[t])))
                          return "ETokens." + r;
                    }
                    if (
                      null != e.Third &&
                      e.hasOwnProperty("Third") &&
                      (r = y.msg.ThirdPartyInfo.verify(e.Third))
                    )
                      return "Third." + r;
                    if (
                      null != e.StageRecord &&
                      e.hasOwnProperty("StageRecord")
                    ) {
                      if (!Array.isArray(e.StageRecord))
                        return "StageRecord: array expected";
                      for (t = 0; t < e.StageRecord.length; ++t)
                        if (!f.isInteger(e.StageRecord[t]))
                          return "StageRecord: integer[] expected";
                    }
                    if (null != e.MTokens && e.hasOwnProperty("MTokens")) {
                      if (!Array.isArray(e.MTokens))
                        return "MTokens: array expected";
                      for (t = 0; t < e.MTokens.length; ++t) {
                        var r;
                        if ((r = y.msg.MaterialToken.verify(e.MTokens[t])))
                          return "MTokens." + r;
                      }
                    }
                    return null != e.BField &&
                      e.hasOwnProperty("BField") &&
                      (r = y.msg.BattleField.verify(e.BField))
                      ? "BField." + r
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.PlayerData) return e;
                    var t = new y.msg.PlayerData();
                    if (null != e.User) {
                      if ("object" != typeof e.User)
                        throw TypeError(
                          ".msg.PlayerData.User: object expected"
                        );
                      t.User = y.msg.UserPart.fromObject(e.User);
                    }
                    if (e.HTokens) {
                      if (!Array.isArray(e.HTokens))
                        throw TypeError(
                          ".msg.PlayerData.HTokens: array expected"
                        );
                      t.HTokens = [];
                      for (var r = 0; r < e.HTokens.length; ++r) {
                        if ("object" != typeof e.HTokens[r])
                          throw TypeError(
                            ".msg.PlayerData.HTokens: object expected"
                          );
                        t.HTokens[r] = y.msg.HeroToken.fromObject(e.HTokens[r]);
                      }
                    }
                    if (e.ETokens) {
                      if (!Array.isArray(e.ETokens))
                        throw TypeError(
                          ".msg.PlayerData.ETokens: array expected"
                        );
                      for (t.ETokens = [], r = 0; r < e.ETokens.length; ++r) {
                        if ("object" != typeof e.ETokens[r])
                          throw TypeError(
                            ".msg.PlayerData.ETokens: object expected"
                          );
                        t.ETokens[r] = y.msg.EquipmentToken.fromObject(
                          e.ETokens[r]
                        );
                      }
                    }
                    if (null != e.Third) {
                      if ("object" != typeof e.Third)
                        throw TypeError(
                          ".msg.PlayerData.Third: object expected"
                        );
                      t.Third = y.msg.ThirdPartyInfo.fromObject(e.Third);
                    }
                    if (e.StageRecord) {
                      if (!Array.isArray(e.StageRecord))
                        throw TypeError(
                          ".msg.PlayerData.StageRecord: array expected"
                        );
                      for (
                        t.StageRecord = [], r = 0;
                        r < e.StageRecord.length;
                        ++r
                      )
                        t.StageRecord[r] = 0 | e.StageRecord[r];
                    }
                    if (e.MTokens) {
                      if (!Array.isArray(e.MTokens))
                        throw TypeError(
                          ".msg.PlayerData.MTokens: array expected"
                        );
                      for (t.MTokens = [], r = 0; r < e.MTokens.length; ++r) {
                        if ("object" != typeof e.MTokens[r])
                          throw TypeError(
                            ".msg.PlayerData.MTokens: object expected"
                          );
                        t.MTokens[r] = y.msg.MaterialToken.fromObject(
                          e.MTokens[r]
                        );
                      }
                    }
                    if (null != e.BField) {
                      if ("object" != typeof e.BField)
                        throw TypeError(
                          ".msg.PlayerData.BField: object expected"
                        );
                      t.BField = y.msg.BattleField.fromObject(e.BField);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.HTokens = []),
                        (r.ETokens = []),
                        (r.StageRecord = []),
                        (r.MTokens = [])),
                      t.defaults &&
                        ((r.User = null), (r.Third = null), (r.BField = null)),
                      null != e.User &&
                        e.hasOwnProperty("User") &&
                        (r.User = y.msg.UserPart.toObject(e.User, t)),
                      e.HTokens && e.HTokens.length)
                    ) {
                      r.HTokens = [];
                      for (var n = 0; n < e.HTokens.length; ++n)
                        r.HTokens[n] = y.msg.HeroToken.toObject(
                          e.HTokens[n],
                          t
                        );
                    }
                    if (e.ETokens && e.ETokens.length)
                      for (r.ETokens = [], n = 0; n < e.ETokens.length; ++n)
                        r.ETokens[n] = y.msg.EquipmentToken.toObject(
                          e.ETokens[n],
                          t
                        );
                    if (
                      (null != e.Third &&
                        e.hasOwnProperty("Third") &&
                        (r.Third = y.msg.ThirdPartyInfo.toObject(e.Third, t)),
                      e.StageRecord && e.StageRecord.length)
                    )
                      for (
                        r.StageRecord = [], n = 0;
                        n < e.StageRecord.length;
                        ++n
                      )
                        r.StageRecord[n] = e.StageRecord[n];
                    if (e.MTokens && e.MTokens.length)
                      for (r.MTokens = [], n = 0; n < e.MTokens.length; ++n)
                        r.MTokens[n] = y.msg.MaterialToken.toObject(
                          e.MTokens[n],
                          t
                        );
                    return (
                      null != e.BField &&
                        e.hasOwnProperty("BField") &&
                        (r.BField = y.msg.BattleField.toObject(e.BField, t)),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.UserPart = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.UserID = f.Long ? f.Long.fromBits(0, 0, !1) : 0),
                  (e.prototype.OpenID = ""),
                  (e.prototype.CreateTime = 0),
                  (e.prototype.C1 = ""),
                  (e.prototype.C2 = ""),
                  (e.prototype.Vit = 0),
                  (e.prototype.Setting = ""),
                  (e.prototype.Activity = 0),
                  (e.prototype.MainHeroSN = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.UserID &&
                        Object.hasOwnProperty.call(e, "UserID") &&
                        t.uint32(8).int64(e.UserID),
                      null != e.OpenID &&
                        Object.hasOwnProperty.call(e, "OpenID") &&
                        t.uint32(18).string(e.OpenID),
                      null != e.CreateTime &&
                        Object.hasOwnProperty.call(e, "CreateTime") &&
                        t.uint32(24).uint32(e.CreateTime),
                      null != e.C1 &&
                        Object.hasOwnProperty.call(e, "C1") &&
                        t.uint32(34).string(e.C1),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(42).string(e.C2),
                      null != e.Vit &&
                        Object.hasOwnProperty.call(e, "Vit") &&
                        t.uint32(48).int32(e.Vit),
                      null != e.Setting &&
                        Object.hasOwnProperty.call(e, "Setting") &&
                        t.uint32(58).string(e.Setting),
                      null != e.Activity &&
                        Object.hasOwnProperty.call(e, "Activity") &&
                        t.uint32(64).int32(e.Activity),
                      null != e.MainHeroSN &&
                        Object.hasOwnProperty.call(e, "MainHeroSN") &&
                        t.uint32(72).uint32(e.MainHeroSN),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.UserPart();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.UserID = e.int64();
                          break;
                        case 2:
                          n.OpenID = e.string();
                          break;
                        case 3:
                          n.CreateTime = e.uint32();
                          break;
                        case 4:
                          n.C1 = e.string();
                          break;
                        case 5:
                          n.C2 = e.string();
                          break;
                        case 6:
                          n.Vit = e.int32();
                          break;
                        case 7:
                          n.Setting = e.string();
                          break;
                        case 8:
                          n.Activity = e.int32();
                          break;
                        case 9:
                          n.MainHeroSN = e.uint32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.UserID &&
                        e.hasOwnProperty("UserID") &&
                        !(
                          f.isInteger(e.UserID) ||
                          (e.UserID &&
                            f.isInteger(e.UserID.low) &&
                            f.isInteger(e.UserID.high))
                        )
                      ? "UserID: integer|Long expected"
                      : null != e.OpenID &&
                        e.hasOwnProperty("OpenID") &&
                        !f.isString(e.OpenID)
                      ? "OpenID: string expected"
                      : null != e.CreateTime &&
                        e.hasOwnProperty("CreateTime") &&
                        !f.isInteger(e.CreateTime)
                      ? "CreateTime: integer expected"
                      : null != e.C1 &&
                        e.hasOwnProperty("C1") &&
                        !f.isString(e.C1)
                      ? "C1: string expected"
                      : null != e.C2 &&
                        e.hasOwnProperty("C2") &&
                        !f.isString(e.C2)
                      ? "C2: string expected"
                      : null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        !f.isInteger(e.Vit)
                      ? "Vit: integer expected"
                      : null != e.Setting &&
                        e.hasOwnProperty("Setting") &&
                        !f.isString(e.Setting)
                      ? "Setting: string expected"
                      : null != e.Activity &&
                        e.hasOwnProperty("Activity") &&
                        !f.isInteger(e.Activity)
                      ? "Activity: integer expected"
                      : null != e.MainHeroSN &&
                        e.hasOwnProperty("MainHeroSN") &&
                        !f.isInteger(e.MainHeroSN)
                      ? "MainHeroSN: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.UserPart) return e;
                    var t = new y.msg.UserPart();
                    return (
                      null != e.UserID &&
                        (f.Long
                          ? ((t.UserID = f.Long.fromValue(e.UserID)).unsigned =
                              !1)
                          : "string" == typeof e.UserID
                          ? (t.UserID = parseInt(e.UserID, 10))
                          : "number" == typeof e.UserID
                          ? (t.UserID = e.UserID)
                          : "object" == typeof e.UserID &&
                            (t.UserID = new f.LongBits(
                              e.UserID.low >>> 0,
                              e.UserID.high >>> 0
                            ).toNumber())),
                      null != e.OpenID && (t.OpenID = String(e.OpenID)),
                      null != e.CreateTime &&
                        (t.CreateTime = e.CreateTime >>> 0),
                      null != e.C1 && (t.C1 = String(e.C1)),
                      null != e.C2 && (t.C2 = String(e.C2)),
                      null != e.Vit && (t.Vit = 0 | e.Vit),
                      null != e.Setting && (t.Setting = String(e.Setting)),
                      null != e.Activity && (t.Activity = 0 | e.Activity),
                      null != e.MainHeroSN &&
                        (t.MainHeroSN = e.MainHeroSN >>> 0),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (t.defaults) {
                      if (f.Long) {
                        var n = new f.Long(0, 0, !1);
                        r.UserID =
                          t.longs === String
                            ? n.toString()
                            : t.longs === Number
                            ? n.toNumber()
                            : n;
                      } else r.UserID = t.longs === String ? "0" : 0;
                      (r.OpenID = ""),
                        (r.CreateTime = 0),
                        (r.C1 = ""),
                        (r.C2 = ""),
                        (r.Vit = 0),
                        (r.Setting = ""),
                        (r.Activity = 0),
                        (r.MainHeroSN = 0);
                    }
                    return (
                      null != e.UserID &&
                        e.hasOwnProperty("UserID") &&
                        ("number" == typeof e.UserID
                          ? (r.UserID =
                              t.longs === String ? String(e.UserID) : e.UserID)
                          : (r.UserID =
                              t.longs === String
                                ? f.Long.prototype.toString.call(e.UserID)
                                : t.longs === Number
                                ? new f.LongBits(
                                    e.UserID.low >>> 0,
                                    e.UserID.high >>> 0
                                  ).toNumber()
                                : e.UserID)),
                      null != e.OpenID &&
                        e.hasOwnProperty("OpenID") &&
                        (r.OpenID = e.OpenID),
                      null != e.CreateTime &&
                        e.hasOwnProperty("CreateTime") &&
                        (r.CreateTime = e.CreateTime),
                      null != e.C1 && e.hasOwnProperty("C1") && (r.C1 = e.C1),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        (r.Vit = e.Vit),
                      null != e.Setting &&
                        e.hasOwnProperty("Setting") &&
                        (r.Setting = e.Setting),
                      null != e.Activity &&
                        e.hasOwnProperty("Activity") &&
                        (r.Activity = e.Activity),
                      null != e.MainHeroSN &&
                        e.hasOwnProperty("MainHeroSN") &&
                        (r.MainHeroSN = e.MainHeroSN),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.HeroToken = (function () {
                function e(e) {
                  if (((this.TIDAry = []), (this.ESNAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.TIDAry = f.emptyArray),
                  (e.prototype.ESNAry = f.emptyArray),
                  (e.prototype.Vit = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).uint32(e.SN),
                      null != e.TIDAry && e.TIDAry.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.TIDAry.length; ++r)
                        t.int32(e.TIDAry[r]);
                      t.ldelim();
                    }
                    if (null != e.ESNAry && e.ESNAry.length) {
                      for (t.uint32(26).fork(), r = 0; r < e.ESNAry.length; ++r)
                        t.uint32(e.ESNAry[r]);
                      t.ldelim();
                    }
                    return (
                      null != e.Vit &&
                        Object.hasOwnProperty.call(e, "Vit") &&
                        t.uint32(32).int32(e.Vit),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.HeroToken();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.uint32();
                          break;
                        case 2:
                          if (
                            ((n.TIDAry && n.TIDAry.length) || (n.TIDAry = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.TIDAry.push(e.int32());
                          else n.TIDAry.push(e.int32());
                          break;
                        case 3:
                          if (
                            ((n.ESNAry && n.ESNAry.length) || (n.ESNAry = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.ESNAry.push(e.uint32());
                          else n.ESNAry.push(e.uint32());
                          break;
                        case 4:
                          n.Vit = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.SN &&
                      e.hasOwnProperty("SN") &&
                      !f.isInteger(e.SN)
                    )
                      return "SN: integer expected";
                    if (null != e.TIDAry && e.hasOwnProperty("TIDAry")) {
                      if (!Array.isArray(e.TIDAry))
                        return "TIDAry: array expected";
                      for (var t = 0; t < e.TIDAry.length; ++t)
                        if (!f.isInteger(e.TIDAry[t]))
                          return "TIDAry: integer[] expected";
                    }
                    if (null != e.ESNAry && e.hasOwnProperty("ESNAry")) {
                      if (!Array.isArray(e.ESNAry))
                        return "ESNAry: array expected";
                      for (t = 0; t < e.ESNAry.length; ++t)
                        if (!f.isInteger(e.ESNAry[t]))
                          return "ESNAry: integer[] expected";
                    }
                    return null != e.Vit &&
                      e.hasOwnProperty("Vit") &&
                      !f.isInteger(e.Vit)
                      ? "Vit: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.HeroToken) return e;
                    var t = new y.msg.HeroToken();
                    if ((null != e.SN && (t.SN = e.SN >>> 0), e.TIDAry)) {
                      if (!Array.isArray(e.TIDAry))
                        throw TypeError(
                          ".msg.HeroToken.TIDAry: array expected"
                        );
                      t.TIDAry = [];
                      for (var r = 0; r < e.TIDAry.length; ++r)
                        t.TIDAry[r] = 0 | e.TIDAry[r];
                    }
                    if (e.ESNAry) {
                      if (!Array.isArray(e.ESNAry))
                        throw TypeError(
                          ".msg.HeroToken.ESNAry: array expected"
                        );
                      for (t.ESNAry = [], r = 0; r < e.ESNAry.length; ++r)
                        t.ESNAry[r] = e.ESNAry[r] >>> 0;
                    }
                    return null != e.Vit && (t.Vit = 0 | e.Vit), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.TIDAry = []), (r.ESNAry = [])),
                      t.defaults && ((r.SN = 0), (r.Vit = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      e.TIDAry && e.TIDAry.length)
                    ) {
                      r.TIDAry = [];
                      for (var n = 0; n < e.TIDAry.length; ++n)
                        r.TIDAry[n] = e.TIDAry[n];
                    }
                    if (e.ESNAry && e.ESNAry.length)
                      for (r.ESNAry = [], n = 0; n < e.ESNAry.length; ++n)
                        r.ESNAry[n] = e.ESNAry[n];
                    return (
                      null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        (r.Vit = e.Vit),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.EquipmentToken = (function () {
                function e(e) {
                  if (((this.TIDAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.TIDAry = f.emptyArray),
                  (e.prototype.Durability = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).uint32(e.SN),
                      null != e.TIDAry && e.TIDAry.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.TIDAry.length; ++r)
                        t.int32(e.TIDAry[r]);
                      t.ldelim();
                    }
                    return (
                      null != e.Durability &&
                        Object.hasOwnProperty.call(e, "Durability") &&
                        t.uint32(24).int32(e.Durability),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.EquipmentToken();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.uint32();
                          break;
                        case 2:
                          if (
                            ((n.TIDAry && n.TIDAry.length) || (n.TIDAry = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.TIDAry.push(e.int32());
                          else n.TIDAry.push(e.int32());
                          break;
                        case 3:
                          n.Durability = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.SN &&
                      e.hasOwnProperty("SN") &&
                      !f.isInteger(e.SN)
                    )
                      return "SN: integer expected";
                    if (null != e.TIDAry && e.hasOwnProperty("TIDAry")) {
                      if (!Array.isArray(e.TIDAry))
                        return "TIDAry: array expected";
                      for (var t = 0; t < e.TIDAry.length; ++t)
                        if (!f.isInteger(e.TIDAry[t]))
                          return "TIDAry: integer[] expected";
                    }
                    return null != e.Durability &&
                      e.hasOwnProperty("Durability") &&
                      !f.isInteger(e.Durability)
                      ? "Durability: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.EquipmentToken) return e;
                    var t = new y.msg.EquipmentToken();
                    if ((null != e.SN && (t.SN = e.SN >>> 0), e.TIDAry)) {
                      if (!Array.isArray(e.TIDAry))
                        throw TypeError(
                          ".msg.EquipmentToken.TIDAry: array expected"
                        );
                      t.TIDAry = [];
                      for (var r = 0; r < e.TIDAry.length; ++r)
                        t.TIDAry[r] = 0 | e.TIDAry[r];
                    }
                    return (
                      null != e.Durability && (t.Durability = 0 | e.Durability),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.TIDAry = []),
                      t.defaults && ((r.SN = 0), (r.Durability = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      e.TIDAry && e.TIDAry.length)
                    ) {
                      r.TIDAry = [];
                      for (var n = 0; n < e.TIDAry.length; ++n)
                        r.TIDAry[n] = e.TIDAry[n];
                    }
                    return (
                      null != e.Durability &&
                        e.hasOwnProperty("Durability") &&
                        (r.Durability = e.Durability),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.Hero2Equipment = (function () {
                function e(e) {
                  if (((this.ESNAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.HeroSN = 0),
                  (e.prototype.ESNAry = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.HeroSN &&
                        Object.hasOwnProperty.call(e, "HeroSN") &&
                        t.uint32(8).uint32(e.HeroSN),
                      null != e.ESNAry && e.ESNAry.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.ESNAry.length; ++r)
                        t.uint32(e.ESNAry[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.Hero2Equipment();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.HeroSN = e.uint32();
                          break;
                        case 2:
                          if (
                            ((n.ESNAry && n.ESNAry.length) || (n.ESNAry = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.ESNAry.push(e.uint32());
                          else n.ESNAry.push(e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.HeroSN &&
                      e.hasOwnProperty("HeroSN") &&
                      !f.isInteger(e.HeroSN)
                    )
                      return "HeroSN: integer expected";
                    if (null != e.ESNAry && e.hasOwnProperty("ESNAry")) {
                      if (!Array.isArray(e.ESNAry))
                        return "ESNAry: array expected";
                      for (var t = 0; t < e.ESNAry.length; ++t)
                        if (!f.isInteger(e.ESNAry[t]))
                          return "ESNAry: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.Hero2Equipment) return e;
                    var t = new y.msg.Hero2Equipment();
                    if (
                      (null != e.HeroSN && (t.HeroSN = e.HeroSN >>> 0),
                      e.ESNAry)
                    ) {
                      if (!Array.isArray(e.ESNAry))
                        throw TypeError(
                          ".msg.Hero2Equipment.ESNAry: array expected"
                        );
                      t.ESNAry = [];
                      for (var r = 0; r < e.ESNAry.length; ++r)
                        t.ESNAry[r] = e.ESNAry[r] >>> 0;
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.ESNAry = []),
                      t.defaults && (r.HeroSN = 0),
                      null != e.HeroSN &&
                        e.hasOwnProperty("HeroSN") &&
                        (r.HeroSN = e.HeroSN),
                      e.ESNAry && e.ESNAry.length)
                    ) {
                      r.ESNAry = [];
                      for (var n = 0; n < e.ESNAry.length; ++n)
                        r.ESNAry[n] = e.ESNAry[n];
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.ThirdPartyInfo = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Nick = ""),
                  (e.prototype.AvatarUrl = ""),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Nick &&
                        Object.hasOwnProperty.call(e, "Nick") &&
                        t.uint32(10).string(e.Nick),
                      null != e.AvatarUrl &&
                        Object.hasOwnProperty.call(e, "AvatarUrl") &&
                        t.uint32(18).string(e.AvatarUrl),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.ThirdPartyInfo();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Nick = e.string();
                          break;
                        case 2:
                          n.AvatarUrl = e.string();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Nick &&
                        e.hasOwnProperty("Nick") &&
                        !f.isString(e.Nick)
                      ? "Nick: string expected"
                      : null != e.AvatarUrl &&
                        e.hasOwnProperty("AvatarUrl") &&
                        !f.isString(e.AvatarUrl)
                      ? "AvatarUrl: string expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.ThirdPartyInfo) return e;
                    var t = new y.msg.ThirdPartyInfo();
                    return (
                      null != e.Nick && (t.Nick = String(e.Nick)),
                      null != e.AvatarUrl &&
                        (t.AvatarUrl = String(e.AvatarUrl)),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.Nick = ""), (r.AvatarUrl = "")),
                      null != e.Nick &&
                        e.hasOwnProperty("Nick") &&
                        (r.Nick = e.Nick),
                      null != e.AvatarUrl &&
                        e.hasOwnProperty("AvatarUrl") &&
                        (r.AvatarUrl = e.AvatarUrl),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.MaterialToken = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.ID = 0),
                  (e.prototype.Num = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.ID &&
                        Object.hasOwnProperty.call(e, "ID") &&
                        t.uint32(8).int32(e.ID),
                      null != e.Num &&
                        Object.hasOwnProperty.call(e, "Num") &&
                        t.uint32(16).int32(e.Num),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.MaterialToken();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.ID = e.int32();
                          break;
                        case 2:
                          n.Num = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.ID &&
                        e.hasOwnProperty("ID") &&
                        !f.isInteger(e.ID)
                      ? "ID: integer expected"
                      : null != e.Num &&
                        e.hasOwnProperty("Num") &&
                        !f.isInteger(e.Num)
                      ? "Num: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.MaterialToken) return e;
                    var t = new y.msg.MaterialToken();
                    return (
                      null != e.ID && (t.ID = 0 | e.ID),
                      null != e.Num && (t.Num = 0 | e.Num),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.ID = 0), (r.Num = 0)),
                      null != e.ID && e.hasOwnProperty("ID") && (r.ID = e.ID),
                      null != e.Num &&
                        e.hasOwnProperty("Num") &&
                        (r.Num = e.Num),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.HeroVit = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.Vit = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).uint32(e.SN),
                      null != e.Vit &&
                        Object.hasOwnProperty.call(e, "Vit") &&
                        t.uint32(16).int32(e.Vit),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.HeroVit();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.uint32();
                          break;
                        case 2:
                          n.Vit = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.SN &&
                        e.hasOwnProperty("SN") &&
                        !f.isInteger(e.SN)
                      ? "SN: integer expected"
                      : null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        !f.isInteger(e.Vit)
                      ? "Vit: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.HeroVit) return e;
                    var t = new y.msg.HeroVit();
                    return (
                      null != e.SN && (t.SN = e.SN >>> 0),
                      null != e.Vit && (t.Vit = 0 | e.Vit),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.SN = 0), (r.Vit = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      null != e.Vit &&
                        e.hasOwnProperty("Vit") &&
                        (r.Vit = e.Vit),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleField = (function () {
                function e(e) {
                  if (((this.CharAttr = []), (this.Weapons = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Ctx = null),
                  (e.prototype.CharAttr = f.emptyArray),
                  (e.prototype.Weapons = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Ctx &&
                        Object.hasOwnProperty.call(e, "Ctx") &&
                        y.msg.BattleContext.encode(
                          e.Ctx,
                          t.uint32(10).fork()
                        ).ldelim(),
                      null != e.CharAttr && e.CharAttr.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.CharAttr.length; ++r)
                        t.int32(e.CharAttr[r]);
                      t.ldelim();
                    }
                    if (null != e.Weapons && e.Weapons.length)
                      for (r = 0; r < e.Weapons.length; ++r)
                        y.msg.BattleWeapon.encode(
                          e.Weapons[r],
                          t.uint32(26).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleField();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Ctx = y.msg.BattleContext.decode(e, e.uint32());
                          break;
                        case 2:
                          if (
                            ((n.CharAttr && n.CharAttr.length) ||
                              (n.CharAttr = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.CharAttr.push(e.int32());
                          else n.CharAttr.push(e.int32());
                          break;
                        case 3:
                          (n.Weapons && n.Weapons.length) || (n.Weapons = []),
                            n.Weapons.push(
                              y.msg.BattleWeapon.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Ctx &&
                      e.hasOwnProperty("Ctx") &&
                      (r = y.msg.BattleContext.verify(e.Ctx))
                    )
                      return "Ctx." + r;
                    if (null != e.CharAttr && e.hasOwnProperty("CharAttr")) {
                      if (!Array.isArray(e.CharAttr))
                        return "CharAttr: array expected";
                      for (var t = 0; t < e.CharAttr.length; ++t)
                        if (!f.isInteger(e.CharAttr[t]))
                          return "CharAttr: integer[] expected";
                    }
                    if (null != e.Weapons && e.hasOwnProperty("Weapons")) {
                      if (!Array.isArray(e.Weapons))
                        return "Weapons: array expected";
                      for (t = 0; t < e.Weapons.length; ++t) {
                        var r;
                        if ((r = y.msg.BattleWeapon.verify(e.Weapons[t])))
                          return "Weapons." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleField) return e;
                    var t = new y.msg.BattleField();
                    if (null != e.Ctx) {
                      if ("object" != typeof e.Ctx)
                        throw TypeError(
                          ".msg.BattleField.Ctx: object expected"
                        );
                      t.Ctx = y.msg.BattleContext.fromObject(e.Ctx);
                    }
                    if (e.CharAttr) {
                      if (!Array.isArray(e.CharAttr))
                        throw TypeError(
                          ".msg.BattleField.CharAttr: array expected"
                        );
                      t.CharAttr = [];
                      for (var r = 0; r < e.CharAttr.length; ++r)
                        t.CharAttr[r] = 0 | e.CharAttr[r];
                    }
                    if (e.Weapons) {
                      if (!Array.isArray(e.Weapons))
                        throw TypeError(
                          ".msg.BattleField.Weapons: array expected"
                        );
                      for (t.Weapons = [], r = 0; r < e.Weapons.length; ++r) {
                        if ("object" != typeof e.Weapons[r])
                          throw TypeError(
                            ".msg.BattleField.Weapons: object expected"
                          );
                        t.Weapons[r] = y.msg.BattleWeapon.fromObject(
                          e.Weapons[r]
                        );
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.CharAttr = []), (r.Weapons = [])),
                      t.defaults && (r.Ctx = null),
                      null != e.Ctx &&
                        e.hasOwnProperty("Ctx") &&
                        (r.Ctx = y.msg.BattleContext.toObject(e.Ctx, t)),
                      e.CharAttr && e.CharAttr.length)
                    ) {
                      r.CharAttr = [];
                      for (var n = 0; n < e.CharAttr.length; ++n)
                        r.CharAttr[n] = e.CharAttr[n];
                    }
                    if (e.Weapons && e.Weapons.length)
                      for (r.Weapons = [], n = 0; n < e.Weapons.length; ++n)
                        r.Weapons[n] = y.msg.BattleWeapon.toObject(
                          e.Weapons[n],
                          t
                        );
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleContext = (function () {
                function e(e) {
                  if (
                    ((this.HeroTID = []),
                    (this.Equips = []),
                    (this.Weapons = []),
                    (this.Items = []),
                    (this.TileItem = []),
                    (this.AttrEx = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Chapter = 0),
                  (e.prototype.Stage = 0),
                  (e.prototype.HeroTID = f.emptyArray),
                  (e.prototype.ClassID = 0),
                  (e.prototype.HP = 0),
                  (e.prototype.Gold = 0),
                  (e.prototype.State = 0),
                  (e.prototype.Equips = f.emptyArray),
                  (e.prototype.Weapons = f.emptyArray),
                  (e.prototype.Items = f.emptyArray),
                  (e.prototype.TileItem = f.emptyArray),
                  (e.prototype.Shop = null),
                  (e.prototype.AttrEx = f.emptyArray),
                  (e.prototype.Drop = null),
                  (e.prototype.Score = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Chapter &&
                        Object.hasOwnProperty.call(e, "Chapter") &&
                        t.uint32(8).int32(e.Chapter),
                      null != e.Stage &&
                        Object.hasOwnProperty.call(e, "Stage") &&
                        t.uint32(16).int32(e.Stage),
                      null != e.HeroTID && e.HeroTID.length)
                    ) {
                      t.uint32(26).fork();
                      for (var r = 0; r < e.HeroTID.length; ++r)
                        t.int32(e.HeroTID[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.ClassID &&
                        Object.hasOwnProperty.call(e, "ClassID") &&
                        t.uint32(32).int32(e.ClassID),
                      null != e.HP &&
                        Object.hasOwnProperty.call(e, "HP") &&
                        t.uint32(40).int32(e.HP),
                      null != e.Gold &&
                        Object.hasOwnProperty.call(e, "Gold") &&
                        t.uint32(48).int32(e.Gold),
                      null != e.State &&
                        Object.hasOwnProperty.call(e, "State") &&
                        t.uint32(56).int32(e.State),
                      null != e.Equips && e.Equips.length)
                    ) {
                      for (t.uint32(66).fork(), r = 0; r < e.Equips.length; ++r)
                        t.int32(e.Equips[r]);
                      t.ldelim();
                    }
                    if (null != e.Weapons && e.Weapons.length) {
                      for (
                        t.uint32(74).fork(), r = 0;
                        r < e.Weapons.length;
                        ++r
                      )
                        t.int32(e.Weapons[r]);
                      t.ldelim();
                    }
                    if (null != e.Items && e.Items.length) {
                      for (t.uint32(82).fork(), r = 0; r < e.Items.length; ++r)
                        t.int32(e.Items[r]);
                      t.ldelim();
                    }
                    if (null != e.TileItem && e.TileItem.length)
                      for (r = 0; r < e.TileItem.length; ++r)
                        y.msg.BattleTileItem.encode(
                          e.TileItem[r],
                          t.uint32(90).fork()
                        ).ldelim();
                    if (
                      (null != e.Shop &&
                        Object.hasOwnProperty.call(e, "Shop") &&
                        y.msg.BattleShop.encode(
                          e.Shop,
                          t.uint32(98).fork()
                        ).ldelim(),
                      null != e.AttrEx && e.AttrEx.length)
                    )
                      for (r = 0; r < e.AttrEx.length; ++r)
                        y.msg.BattleAttrEx.encode(
                          e.AttrEx[r],
                          t.uint32(106).fork()
                        ).ldelim();
                    return (
                      null != e.Drop &&
                        Object.hasOwnProperty.call(e, "Drop") &&
                        y.msg.DropInfo.encode(
                          e.Drop,
                          t.uint32(114).fork()
                        ).ldelim(),
                      null != e.Score &&
                        Object.hasOwnProperty.call(e, "Score") &&
                        t.uint32(120).int32(e.Score),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleContext();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Chapter = e.int32();
                          break;
                        case 2:
                          n.Stage = e.int32();
                          break;
                        case 3:
                          if (
                            ((n.HeroTID && n.HeroTID.length) ||
                              (n.HeroTID = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.HeroTID.push(e.int32());
                          else n.HeroTID.push(e.int32());
                          break;
                        case 4:
                          n.ClassID = e.int32();
                          break;
                        case 5:
                          n.HP = e.int32();
                          break;
                        case 6:
                          n.Gold = e.int32();
                          break;
                        case 7:
                          n.State = e.int32();
                          break;
                        case 8:
                          if (
                            ((n.Equips && n.Equips.length) || (n.Equips = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.Equips.push(e.int32());
                          else n.Equips.push(e.int32());
                          break;
                        case 9:
                          if (
                            ((n.Weapons && n.Weapons.length) ||
                              (n.Weapons = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.Weapons.push(e.int32());
                          else n.Weapons.push(e.int32());
                          break;
                        case 10:
                          if (
                            ((n.Items && n.Items.length) || (n.Items = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.Items.push(e.int32());
                          else n.Items.push(e.int32());
                          break;
                        case 11:
                          (n.TileItem && n.TileItem.length) ||
                            (n.TileItem = []),
                            n.TileItem.push(
                              y.msg.BattleTileItem.decode(e, e.uint32())
                            );
                          break;
                        case 12:
                          n.Shop = y.msg.BattleShop.decode(e, e.uint32());
                          break;
                        case 13:
                          (n.AttrEx && n.AttrEx.length) || (n.AttrEx = []),
                            n.AttrEx.push(
                              y.msg.BattleAttrEx.decode(e, e.uint32())
                            );
                          break;
                        case 14:
                          n.Drop = y.msg.DropInfo.decode(e, e.uint32());
                          break;
                        case 15:
                          n.Score = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Chapter &&
                      e.hasOwnProperty("Chapter") &&
                      !f.isInteger(e.Chapter)
                    )
                      return "Chapter: integer expected";
                    if (
                      null != e.Stage &&
                      e.hasOwnProperty("Stage") &&
                      !f.isInteger(e.Stage)
                    )
                      return "Stage: integer expected";
                    if (null != e.HeroTID && e.hasOwnProperty("HeroTID")) {
                      if (!Array.isArray(e.HeroTID))
                        return "HeroTID: array expected";
                      for (var t = 0; t < e.HeroTID.length; ++t)
                        if (!f.isInteger(e.HeroTID[t]))
                          return "HeroTID: integer[] expected";
                    }
                    if (
                      null != e.ClassID &&
                      e.hasOwnProperty("ClassID") &&
                      !f.isInteger(e.ClassID)
                    )
                      return "ClassID: integer expected";
                    if (
                      null != e.HP &&
                      e.hasOwnProperty("HP") &&
                      !f.isInteger(e.HP)
                    )
                      return "HP: integer expected";
                    if (
                      null != e.Gold &&
                      e.hasOwnProperty("Gold") &&
                      !f.isInteger(e.Gold)
                    )
                      return "Gold: integer expected";
                    if (null != e.State && e.hasOwnProperty("State"))
                      switch (e.State) {
                        default:
                          return "State: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                      }
                    if (null != e.Equips && e.hasOwnProperty("Equips")) {
                      if (!Array.isArray(e.Equips))
                        return "Equips: array expected";
                      for (t = 0; t < e.Equips.length; ++t)
                        if (!f.isInteger(e.Equips[t]))
                          return "Equips: integer[] expected";
                    }
                    if (null != e.Weapons && e.hasOwnProperty("Weapons")) {
                      if (!Array.isArray(e.Weapons))
                        return "Weapons: array expected";
                      for (t = 0; t < e.Weapons.length; ++t)
                        if (!f.isInteger(e.Weapons[t]))
                          return "Weapons: integer[] expected";
                    }
                    if (null != e.Items && e.hasOwnProperty("Items")) {
                      if (!Array.isArray(e.Items))
                        return "Items: array expected";
                      for (t = 0; t < e.Items.length; ++t)
                        if (!f.isInteger(e.Items[t]))
                          return "Items: integer[] expected";
                    }
                    if (null != e.TileItem && e.hasOwnProperty("TileItem")) {
                      if (!Array.isArray(e.TileItem))
                        return "TileItem: array expected";
                      for (t = 0; t < e.TileItem.length; ++t)
                        if ((r = y.msg.BattleTileItem.verify(e.TileItem[t])))
                          return "TileItem." + r;
                    }
                    if (
                      null != e.Shop &&
                      e.hasOwnProperty("Shop") &&
                      (r = y.msg.BattleShop.verify(e.Shop))
                    )
                      return "Shop." + r;
                    if (null != e.AttrEx && e.hasOwnProperty("AttrEx")) {
                      if (!Array.isArray(e.AttrEx))
                        return "AttrEx: array expected";
                      for (t = 0; t < e.AttrEx.length; ++t) {
                        var r;
                        if ((r = y.msg.BattleAttrEx.verify(e.AttrEx[t])))
                          return "AttrEx." + r;
                      }
                    }
                    return null != e.Drop &&
                      e.hasOwnProperty("Drop") &&
                      (r = y.msg.DropInfo.verify(e.Drop))
                      ? "Drop." + r
                      : null != e.Score &&
                        e.hasOwnProperty("Score") &&
                        !f.isInteger(e.Score)
                      ? "Score: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleContext) return e;
                    var t = new y.msg.BattleContext();
                    if (
                      (null != e.Chapter && (t.Chapter = 0 | e.Chapter),
                      null != e.Stage && (t.Stage = 0 | e.Stage),
                      e.HeroTID)
                    ) {
                      if (!Array.isArray(e.HeroTID))
                        throw TypeError(
                          ".msg.BattleContext.HeroTID: array expected"
                        );
                      t.HeroTID = [];
                      for (var r = 0; r < e.HeroTID.length; ++r)
                        t.HeroTID[r] = 0 | e.HeroTID[r];
                    }
                    switch (
                      (null != e.ClassID && (t.ClassID = 0 | e.ClassID),
                      null != e.HP && (t.HP = 0 | e.HP),
                      null != e.Gold && (t.Gold = 0 | e.Gold),
                      e.State)
                    ) {
                      case "GS_Loading":
                      case 0:
                        t.State = 0;
                        break;
                      case "GS_Spawn":
                      case 1:
                        t.State = 1;
                        break;
                      case "GS_Shopping":
                      case 2:
                        t.State = 2;
                        break;
                      case "GS_Win":
                      case 3:
                        t.State = 3;
                        break;
                      case "GS_Lose":
                      case 4:
                        t.State = 4;
                        break;
                      case "GS_Quit":
                      case 5:
                        t.State = 5;
                        break;
                      case "GS_Exception":
                      case 6:
                        t.State = 6;
                    }
                    if (e.Equips) {
                      if (!Array.isArray(e.Equips))
                        throw TypeError(
                          ".msg.BattleContext.Equips: array expected"
                        );
                      for (t.Equips = [], r = 0; r < e.Equips.length; ++r)
                        t.Equips[r] = 0 | e.Equips[r];
                    }
                    if (e.Weapons) {
                      if (!Array.isArray(e.Weapons))
                        throw TypeError(
                          ".msg.BattleContext.Weapons: array expected"
                        );
                      for (t.Weapons = [], r = 0; r < e.Weapons.length; ++r)
                        t.Weapons[r] = 0 | e.Weapons[r];
                    }
                    if (e.Items) {
                      if (!Array.isArray(e.Items))
                        throw TypeError(
                          ".msg.BattleContext.Items: array expected"
                        );
                      for (t.Items = [], r = 0; r < e.Items.length; ++r)
                        t.Items[r] = 0 | e.Items[r];
                    }
                    if (e.TileItem) {
                      if (!Array.isArray(e.TileItem))
                        throw TypeError(
                          ".msg.BattleContext.TileItem: array expected"
                        );
                      for (t.TileItem = [], r = 0; r < e.TileItem.length; ++r) {
                        if ("object" != typeof e.TileItem[r])
                          throw TypeError(
                            ".msg.BattleContext.TileItem: object expected"
                          );
                        t.TileItem[r] = y.msg.BattleTileItem.fromObject(
                          e.TileItem[r]
                        );
                      }
                    }
                    if (null != e.Shop) {
                      if ("object" != typeof e.Shop)
                        throw TypeError(
                          ".msg.BattleContext.Shop: object expected"
                        );
                      t.Shop = y.msg.BattleShop.fromObject(e.Shop);
                    }
                    if (e.AttrEx) {
                      if (!Array.isArray(e.AttrEx))
                        throw TypeError(
                          ".msg.BattleContext.AttrEx: array expected"
                        );
                      for (t.AttrEx = [], r = 0; r < e.AttrEx.length; ++r) {
                        if ("object" != typeof e.AttrEx[r])
                          throw TypeError(
                            ".msg.BattleContext.AttrEx: object expected"
                          );
                        t.AttrEx[r] = y.msg.BattleAttrEx.fromObject(
                          e.AttrEx[r]
                        );
                      }
                    }
                    if (null != e.Drop) {
                      if ("object" != typeof e.Drop)
                        throw TypeError(
                          ".msg.BattleContext.Drop: object expected"
                        );
                      t.Drop = y.msg.DropInfo.fromObject(e.Drop);
                    }
                    return null != e.Score && (t.Score = 0 | e.Score), t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.HeroTID = []),
                        (r.Equips = []),
                        (r.Weapons = []),
                        (r.Items = []),
                        (r.TileItem = []),
                        (r.AttrEx = [])),
                      t.defaults &&
                        ((r.Chapter = 0),
                        (r.Stage = 0),
                        (r.ClassID = 0),
                        (r.HP = 0),
                        (r.Gold = 0),
                        (r.State = t.enums === String ? "GS_Loading" : 0),
                        (r.Shop = null),
                        (r.Drop = null),
                        (r.Score = 0)),
                      null != e.Chapter &&
                        e.hasOwnProperty("Chapter") &&
                        (r.Chapter = e.Chapter),
                      null != e.Stage &&
                        e.hasOwnProperty("Stage") &&
                        (r.Stage = e.Stage),
                      e.HeroTID && e.HeroTID.length)
                    ) {
                      r.HeroTID = [];
                      for (var n = 0; n < e.HeroTID.length; ++n)
                        r.HeroTID[n] = e.HeroTID[n];
                    }
                    if (
                      (null != e.ClassID &&
                        e.hasOwnProperty("ClassID") &&
                        (r.ClassID = e.ClassID),
                      null != e.HP && e.hasOwnProperty("HP") && (r.HP = e.HP),
                      null != e.Gold &&
                        e.hasOwnProperty("Gold") &&
                        (r.Gold = e.Gold),
                      null != e.State &&
                        e.hasOwnProperty("State") &&
                        (r.State =
                          t.enums === String
                            ? y.msg.GameState[e.State]
                            : e.State),
                      e.Equips && e.Equips.length)
                    )
                      for (r.Equips = [], n = 0; n < e.Equips.length; ++n)
                        r.Equips[n] = e.Equips[n];
                    if (e.Weapons && e.Weapons.length)
                      for (r.Weapons = [], n = 0; n < e.Weapons.length; ++n)
                        r.Weapons[n] = e.Weapons[n];
                    if (e.Items && e.Items.length)
                      for (r.Items = [], n = 0; n < e.Items.length; ++n)
                        r.Items[n] = e.Items[n];
                    if (e.TileItem && e.TileItem.length)
                      for (r.TileItem = [], n = 0; n < e.TileItem.length; ++n)
                        r.TileItem[n] = y.msg.BattleTileItem.toObject(
                          e.TileItem[n],
                          t
                        );
                    if (
                      (null != e.Shop &&
                        e.hasOwnProperty("Shop") &&
                        (r.Shop = y.msg.BattleShop.toObject(e.Shop, t)),
                      e.AttrEx && e.AttrEx.length)
                    )
                      for (r.AttrEx = [], n = 0; n < e.AttrEx.length; ++n)
                        r.AttrEx[n] = y.msg.BattleAttrEx.toObject(
                          e.AttrEx[n],
                          t
                        );
                    return (
                      null != e.Drop &&
                        e.hasOwnProperty("Drop") &&
                        (r.Drop = y.msg.DropInfo.toObject(e.Drop, t)),
                      null != e.Score &&
                        e.hasOwnProperty("Score") &&
                        (r.Score = e.Score),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleWeapon = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.WID = 0),
                  (e.prototype.Dmg = 0),
                  (e.prototype.DmgCritical = 0),
                  (e.prototype.AtkRange = 0),
                  (e.prototype.AtkIntl = 0),
                  (e.prototype.Knockback = 0),
                  (e.prototype.AtkMax = 0),
                  (e.prototype.AppendBuff = 0),
                  (e.prototype.AtkCD = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).int32(e.SN),
                      null != e.WID &&
                        Object.hasOwnProperty.call(e, "WID") &&
                        t.uint32(16).int32(e.WID),
                      null != e.Dmg &&
                        Object.hasOwnProperty.call(e, "Dmg") &&
                        t.uint32(24).int32(e.Dmg),
                      null != e.DmgCritical &&
                        Object.hasOwnProperty.call(e, "DmgCritical") &&
                        t.uint32(32).int32(e.DmgCritical),
                      null != e.AtkRange &&
                        Object.hasOwnProperty.call(e, "AtkRange") &&
                        t.uint32(40).int32(e.AtkRange),
                      null != e.AtkIntl &&
                        Object.hasOwnProperty.call(e, "AtkIntl") &&
                        t.uint32(48).int32(e.AtkIntl),
                      null != e.Knockback &&
                        Object.hasOwnProperty.call(e, "Knockback") &&
                        t.uint32(56).int32(e.Knockback),
                      null != e.AtkMax &&
                        Object.hasOwnProperty.call(e, "AtkMax") &&
                        t.uint32(64).int32(e.AtkMax),
                      null != e.AppendBuff &&
                        Object.hasOwnProperty.call(e, "AppendBuff") &&
                        t.uint32(72).int32(e.AppendBuff),
                      null != e.AtkCD &&
                        Object.hasOwnProperty.call(e, "AtkCD") &&
                        t.uint32(80).int32(e.AtkCD),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleWeapon();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.int32();
                          break;
                        case 2:
                          n.WID = e.int32();
                          break;
                        case 3:
                          n.Dmg = e.int32();
                          break;
                        case 4:
                          n.DmgCritical = e.int32();
                          break;
                        case 5:
                          n.AtkRange = e.int32();
                          break;
                        case 6:
                          n.AtkIntl = e.int32();
                          break;
                        case 7:
                          n.Knockback = e.int32();
                          break;
                        case 8:
                          n.AtkMax = e.int32();
                          break;
                        case 9:
                          n.AppendBuff = e.int32();
                          break;
                        case 10:
                          n.AtkCD = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.SN &&
                        e.hasOwnProperty("SN") &&
                        !f.isInteger(e.SN)
                      ? "SN: integer expected"
                      : null != e.WID &&
                        e.hasOwnProperty("WID") &&
                        !f.isInteger(e.WID)
                      ? "WID: integer expected"
                      : null != e.Dmg &&
                        e.hasOwnProperty("Dmg") &&
                        !f.isInteger(e.Dmg)
                      ? "Dmg: integer expected"
                      : null != e.DmgCritical &&
                        e.hasOwnProperty("DmgCritical") &&
                        !f.isInteger(e.DmgCritical)
                      ? "DmgCritical: integer expected"
                      : null != e.AtkRange &&
                        e.hasOwnProperty("AtkRange") &&
                        !f.isInteger(e.AtkRange)
                      ? "AtkRange: integer expected"
                      : null != e.AtkIntl &&
                        e.hasOwnProperty("AtkIntl") &&
                        !f.isInteger(e.AtkIntl)
                      ? "AtkIntl: integer expected"
                      : null != e.Knockback &&
                        e.hasOwnProperty("Knockback") &&
                        !f.isInteger(e.Knockback)
                      ? "Knockback: integer expected"
                      : null != e.AtkMax &&
                        e.hasOwnProperty("AtkMax") &&
                        !f.isInteger(e.AtkMax)
                      ? "AtkMax: integer expected"
                      : null != e.AppendBuff &&
                        e.hasOwnProperty("AppendBuff") &&
                        !f.isInteger(e.AppendBuff)
                      ? "AppendBuff: integer expected"
                      : null != e.AtkCD &&
                        e.hasOwnProperty("AtkCD") &&
                        !f.isInteger(e.AtkCD)
                      ? "AtkCD: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleWeapon) return e;
                    var t = new y.msg.BattleWeapon();
                    return (
                      null != e.SN && (t.SN = 0 | e.SN),
                      null != e.WID && (t.WID = 0 | e.WID),
                      null != e.Dmg && (t.Dmg = 0 | e.Dmg),
                      null != e.DmgCritical &&
                        (t.DmgCritical = 0 | e.DmgCritical),
                      null != e.AtkRange && (t.AtkRange = 0 | e.AtkRange),
                      null != e.AtkIntl && (t.AtkIntl = 0 | e.AtkIntl),
                      null != e.Knockback && (t.Knockback = 0 | e.Knockback),
                      null != e.AtkMax && (t.AtkMax = 0 | e.AtkMax),
                      null != e.AppendBuff && (t.AppendBuff = 0 | e.AppendBuff),
                      null != e.AtkCD && (t.AtkCD = 0 | e.AtkCD),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.SN = 0),
                        (r.WID = 0),
                        (r.Dmg = 0),
                        (r.DmgCritical = 0),
                        (r.AtkRange = 0),
                        (r.AtkIntl = 0),
                        (r.Knockback = 0),
                        (r.AtkMax = 0),
                        (r.AppendBuff = 0),
                        (r.AtkCD = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      null != e.WID &&
                        e.hasOwnProperty("WID") &&
                        (r.WID = e.WID),
                      null != e.Dmg &&
                        e.hasOwnProperty("Dmg") &&
                        (r.Dmg = e.Dmg),
                      null != e.DmgCritical &&
                        e.hasOwnProperty("DmgCritical") &&
                        (r.DmgCritical = e.DmgCritical),
                      null != e.AtkRange &&
                        e.hasOwnProperty("AtkRange") &&
                        (r.AtkRange = e.AtkRange),
                      null != e.AtkIntl &&
                        e.hasOwnProperty("AtkIntl") &&
                        (r.AtkIntl = e.AtkIntl),
                      null != e.Knockback &&
                        e.hasOwnProperty("Knockback") &&
                        (r.Knockback = e.Knockback),
                      null != e.AtkMax &&
                        e.hasOwnProperty("AtkMax") &&
                        (r.AtkMax = e.AtkMax),
                      null != e.AppendBuff &&
                        e.hasOwnProperty("AppendBuff") &&
                        (r.AppendBuff = e.AppendBuff),
                      null != e.AtkCD &&
                        e.hasOwnProperty("AtkCD") &&
                        (r.AtkCD = e.AtkCD),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleWeaponUpdate = (function () {
                function e(e) {
                  if (((this.WATypes = []), (this.WAVals = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.WATypes = f.emptyArray),
                  (e.prototype.WAVals = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).int32(e.SN),
                      null != e.WATypes && e.WATypes.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.WATypes.length; ++r)
                        t.int32(e.WATypes[r]);
                      t.ldelim();
                    }
                    if (null != e.WAVals && e.WAVals.length) {
                      for (t.uint32(26).fork(), r = 0; r < e.WAVals.length; ++r)
                        t.int32(e.WAVals[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleWeaponUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.WATypes && n.WATypes.length) ||
                              (n.WATypes = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.WATypes.push(e.int32());
                          else n.WATypes.push(e.int32());
                          break;
                        case 3:
                          if (
                            ((n.WAVals && n.WAVals.length) || (n.WAVals = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.WAVals.push(e.int32());
                          else n.WAVals.push(e.int32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.SN &&
                      e.hasOwnProperty("SN") &&
                      !f.isInteger(e.SN)
                    )
                      return "SN: integer expected";
                    if (null != e.WATypes && e.hasOwnProperty("WATypes")) {
                      if (!Array.isArray(e.WATypes))
                        return "WATypes: array expected";
                      for (var t = 0; t < e.WATypes.length; ++t)
                        switch (e.WATypes[t]) {
                          default:
                            return "WATypes: enum value[] expected";
                          case 0:
                          case 1:
                          case 2:
                          case 3:
                          case 4:
                          case 5:
                        }
                    }
                    if (null != e.WAVals && e.hasOwnProperty("WAVals")) {
                      if (!Array.isArray(e.WAVals))
                        return "WAVals: array expected";
                      for (t = 0; t < e.WAVals.length; ++t)
                        if (!f.isInteger(e.WAVals[t]))
                          return "WAVals: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleWeaponUpdate) return e;
                    var t = new y.msg.BattleWeaponUpdate();
                    if ((null != e.SN && (t.SN = 0 | e.SN), e.WATypes)) {
                      if (!Array.isArray(e.WATypes))
                        throw TypeError(
                          ".msg.BattleWeaponUpdate.WATypes: array expected"
                        );
                      t.WATypes = [];
                      for (var r = 0; r < e.WATypes.length; ++r)
                        switch (e.WATypes[r]) {
                          default:
                          case "WAT_Dmg":
                          case 0:
                            t.WATypes[r] = 0;
                            break;
                          case "WAT_DmgCritical":
                          case 1:
                            t.WATypes[r] = 1;
                            break;
                          case "WAT_AtkRange":
                          case 2:
                            t.WATypes[r] = 2;
                            break;
                          case "WAT_AtkLength":
                          case 3:
                            t.WATypes[r] = 3;
                            break;
                          case "WAT_Knockback":
                          case 4:
                            t.WATypes[r] = 4;
                            break;
                          case "WAT_DotDmg":
                          case 5:
                            t.WATypes[r] = 5;
                        }
                    }
                    if (e.WAVals) {
                      if (!Array.isArray(e.WAVals))
                        throw TypeError(
                          ".msg.BattleWeaponUpdate.WAVals: array expected"
                        );
                      for (t.WAVals = [], r = 0; r < e.WAVals.length; ++r)
                        t.WAVals[r] = 0 | e.WAVals[r];
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.WATypes = []), (r.WAVals = [])),
                      t.defaults && (r.SN = 0),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      e.WATypes && e.WATypes.length)
                    ) {
                      r.WATypes = [];
                      for (var n = 0; n < e.WATypes.length; ++n)
                        r.WATypes[n] =
                          t.enums === String
                            ? y.msg.WeaponAttrType[e.WATypes[n]]
                            : e.WATypes[n];
                    }
                    if (e.WAVals && e.WAVals.length)
                      for (r.WAVals = [], n = 0; n < e.WAVals.length; ++n)
                        r.WAVals[n] = e.WAVals[n];
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleSpawn = (function () {
                function e(e) {
                  if (((this.From = []), (this.CrtAttr = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.Type = 0),
                  (e.prototype.ID = 0),
                  (e.prototype.From = f.emptyArray),
                  (e.prototype.CrtAttr = f.emptyArray),
                  (e.prototype.AppendBuff = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).int32(e.SN),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(16).int32(e.Type),
                      null != e.ID &&
                        Object.hasOwnProperty.call(e, "ID") &&
                        t.uint32(24).int32(e.ID),
                      null != e.From && e.From.length)
                    ) {
                      t.uint32(34).fork();
                      for (var r = 0; r < e.From.length; ++r)
                        t.int32(e.From[r]);
                      t.ldelim();
                    }
                    if (null != e.CrtAttr && e.CrtAttr.length) {
                      for (
                        t.uint32(42).fork(), r = 0;
                        r < e.CrtAttr.length;
                        ++r
                      )
                        t.int32(e.CrtAttr[r]);
                      t.ldelim();
                    }
                    return (
                      null != e.AppendBuff &&
                        Object.hasOwnProperty.call(e, "AppendBuff") &&
                        t.uint32(48).int32(e.AppendBuff),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleSpawn();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.int32();
                          break;
                        case 2:
                          n.Type = e.int32();
                          break;
                        case 3:
                          n.ID = e.int32();
                          break;
                        case 4:
                          if (
                            ((n.From && n.From.length) || (n.From = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.From.push(e.int32());
                          else n.From.push(e.int32());
                          break;
                        case 5:
                          if (
                            ((n.CrtAttr && n.CrtAttr.length) ||
                              (n.CrtAttr = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.CrtAttr.push(e.int32());
                          else n.CrtAttr.push(e.int32());
                          break;
                        case 6:
                          n.AppendBuff = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.SN &&
                      e.hasOwnProperty("SN") &&
                      !f.isInteger(e.SN)
                    )
                      return "SN: integer expected";
                    if (
                      null != e.Type &&
                      e.hasOwnProperty("Type") &&
                      !f.isInteger(e.Type)
                    )
                      return "Type: integer expected";
                    if (
                      null != e.ID &&
                      e.hasOwnProperty("ID") &&
                      !f.isInteger(e.ID)
                    )
                      return "ID: integer expected";
                    if (null != e.From && e.hasOwnProperty("From")) {
                      if (!Array.isArray(e.From)) return "From: array expected";
                      for (var t = 0; t < e.From.length; ++t)
                        if (!f.isInteger(e.From[t]))
                          return "From: integer[] expected";
                    }
                    if (null != e.CrtAttr && e.hasOwnProperty("CrtAttr")) {
                      if (!Array.isArray(e.CrtAttr))
                        return "CrtAttr: array expected";
                      for (t = 0; t < e.CrtAttr.length; ++t)
                        if (!f.isInteger(e.CrtAttr[t]))
                          return "CrtAttr: integer[] expected";
                    }
                    return null != e.AppendBuff &&
                      e.hasOwnProperty("AppendBuff") &&
                      !f.isInteger(e.AppendBuff)
                      ? "AppendBuff: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleSpawn) return e;
                    var t = new y.msg.BattleSpawn();
                    if (
                      (null != e.SN && (t.SN = 0 | e.SN),
                      null != e.Type && (t.Type = 0 | e.Type),
                      null != e.ID && (t.ID = 0 | e.ID),
                      e.From)
                    ) {
                      if (!Array.isArray(e.From))
                        throw TypeError(
                          ".msg.BattleSpawn.From: array expected"
                        );
                      t.From = [];
                      for (var r = 0; r < e.From.length; ++r)
                        t.From[r] = 0 | e.From[r];
                    }
                    if (e.CrtAttr) {
                      if (!Array.isArray(e.CrtAttr))
                        throw TypeError(
                          ".msg.BattleSpawn.CrtAttr: array expected"
                        );
                      for (t.CrtAttr = [], r = 0; r < e.CrtAttr.length; ++r)
                        t.CrtAttr[r] = 0 | e.CrtAttr[r];
                    }
                    return (
                      null != e.AppendBuff && (t.AppendBuff = 0 | e.AppendBuff),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.From = []), (r.CrtAttr = [])),
                      t.defaults &&
                        ((r.SN = 0),
                        (r.Type = 0),
                        (r.ID = 0),
                        (r.AppendBuff = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      null != e.ID && e.hasOwnProperty("ID") && (r.ID = e.ID),
                      e.From && e.From.length)
                    ) {
                      r.From = [];
                      for (var n = 0; n < e.From.length; ++n)
                        r.From[n] = e.From[n];
                    }
                    if (e.CrtAttr && e.CrtAttr.length)
                      for (r.CrtAttr = [], n = 0; n < e.CrtAttr.length; ++n)
                        r.CrtAttr[n] = e.CrtAttr[n];
                    return (
                      null != e.AppendBuff &&
                        e.hasOwnProperty("AppendBuff") &&
                        (r.AppendBuff = e.AppendBuff),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleAttrUpdate = (function () {
                function e(e) {
                  if (((this.TypeAry = []), (this.ValAry = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.TypeAry = f.emptyArray),
                  (e.prototype.ValAry = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.TypeAry && e.TypeAry.length)
                    ) {
                      t.uint32(10).fork();
                      for (var r = 0; r < e.TypeAry.length; ++r)
                        t.int32(e.TypeAry[r]);
                      t.ldelim();
                    }
                    if (null != e.ValAry && e.ValAry.length) {
                      for (t.uint32(18).fork(), r = 0; r < e.ValAry.length; ++r)
                        t.int32(e.ValAry[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleAttrUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          if (
                            ((n.TypeAry && n.TypeAry.length) ||
                              (n.TypeAry = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.TypeAry.push(e.int32());
                          else n.TypeAry.push(e.int32());
                          break;
                        case 2:
                          if (
                            ((n.ValAry && n.ValAry.length) || (n.ValAry = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.ValAry.push(e.int32());
                          else n.ValAry.push(e.int32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.TypeAry && e.hasOwnProperty("TypeAry")) {
                      if (!Array.isArray(e.TypeAry))
                        return "TypeAry: array expected";
                      for (var t = 0; t < e.TypeAry.length; ++t)
                        if (!f.isInteger(e.TypeAry[t]))
                          return "TypeAry: integer[] expected";
                    }
                    if (null != e.ValAry && e.hasOwnProperty("ValAry")) {
                      if (!Array.isArray(e.ValAry))
                        return "ValAry: array expected";
                      for (t = 0; t < e.ValAry.length; ++t)
                        if (!f.isInteger(e.ValAry[t]))
                          return "ValAry: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleAttrUpdate) return e;
                    var t = new y.msg.BattleAttrUpdate();
                    if (e.TypeAry) {
                      if (!Array.isArray(e.TypeAry))
                        throw TypeError(
                          ".msg.BattleAttrUpdate.TypeAry: array expected"
                        );
                      t.TypeAry = [];
                      for (var r = 0; r < e.TypeAry.length; ++r)
                        t.TypeAry[r] = 0 | e.TypeAry[r];
                    }
                    if (e.ValAry) {
                      if (!Array.isArray(e.ValAry))
                        throw TypeError(
                          ".msg.BattleAttrUpdate.ValAry: array expected"
                        );
                      for (t.ValAry = [], r = 0; r < e.ValAry.length; ++r)
                        t.ValAry[r] = 0 | e.ValAry[r];
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.TypeAry = []), (r.ValAry = [])),
                      e.TypeAry && e.TypeAry.length)
                    ) {
                      r.TypeAry = [];
                      for (var n = 0; n < e.TypeAry.length; ++n)
                        r.TypeAry[n] = e.TypeAry[n];
                    }
                    if (e.ValAry && e.ValAry.length)
                      for (r.ValAry = [], n = 0; n < e.ValAry.length; ++n)
                        r.ValAry[n] = e.ValAry[n];
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleAttrEx = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.IsPct = 0),
                  (e.prototype.Val = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.IsPct &&
                        Object.hasOwnProperty.call(e, "IsPct") &&
                        t.uint32(16).int32(e.IsPct),
                      null != e.Val &&
                        Object.hasOwnProperty.call(e, "Val") &&
                        t.uint32(24).int32(e.Val),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleAttrEx();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          n.IsPct = e.int32();
                          break;
                        case 3:
                          n.Val = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        !f.isInteger(e.Type)
                      ? "Type: integer expected"
                      : null != e.IsPct &&
                        e.hasOwnProperty("IsPct") &&
                        !f.isInteger(e.IsPct)
                      ? "IsPct: integer expected"
                      : null != e.Val &&
                        e.hasOwnProperty("Val") &&
                        !f.isInteger(e.Val)
                      ? "Val: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleAttrEx) return e;
                    var t = new y.msg.BattleAttrEx();
                    return (
                      null != e.Type && (t.Type = 0 | e.Type),
                      null != e.IsPct && (t.IsPct = 0 | e.IsPct),
                      null != e.Val && (t.Val = 0 | e.Val),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.Type = 0), (r.IsPct = 0), (r.Val = 0)),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      null != e.IsPct &&
                        e.hasOwnProperty("IsPct") &&
                        (r.IsPct = e.IsPct),
                      null != e.Val &&
                        e.hasOwnProperty("Val") &&
                        (r.Val = e.Val),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleDmg = (function () {
                function e(e) {
                  if (((this.Pos = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.CrtSN = 0),
                  (e.prototype.Dmg = 0),
                  (e.prototype.Critical = !1),
                  (e.prototype.CriticalRand = 0),
                  (e.prototype.Dodge = !1),
                  (e.prototype.Die = !1),
                  (e.prototype.Pos = f.emptyArray),
                  (e.prototype.Knockback = 0),
                  (e.prototype.AppendBuff = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.CrtSN &&
                        Object.hasOwnProperty.call(e, "CrtSN") &&
                        t.uint32(8).int32(e.CrtSN),
                      null != e.Dmg &&
                        Object.hasOwnProperty.call(e, "Dmg") &&
                        t.uint32(16).int32(e.Dmg),
                      null != e.Critical &&
                        Object.hasOwnProperty.call(e, "Critical") &&
                        t.uint32(24).bool(e.Critical),
                      null != e.CriticalRand &&
                        Object.hasOwnProperty.call(e, "CriticalRand") &&
                        t.uint32(32).int32(e.CriticalRand),
                      null != e.Dodge &&
                        Object.hasOwnProperty.call(e, "Dodge") &&
                        t.uint32(40).bool(e.Dodge),
                      null != e.Die &&
                        Object.hasOwnProperty.call(e, "Die") &&
                        t.uint32(48).bool(e.Die),
                      null != e.Pos && e.Pos.length)
                    ) {
                      t.uint32(58).fork();
                      for (var r = 0; r < e.Pos.length; ++r) t.int32(e.Pos[r]);
                      t.ldelim();
                    }
                    return (
                      null != e.Knockback &&
                        Object.hasOwnProperty.call(e, "Knockback") &&
                        t.uint32(64).int32(e.Knockback),
                      null != e.AppendBuff &&
                        Object.hasOwnProperty.call(e, "AppendBuff") &&
                        y.msg.BattleBuff.encode(
                          e.AppendBuff,
                          t.uint32(74).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleDmg();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.CrtSN = e.int32();
                          break;
                        case 2:
                          n.Dmg = e.int32();
                          break;
                        case 3:
                          n.Critical = e.bool();
                          break;
                        case 4:
                          n.CriticalRand = e.int32();
                          break;
                        case 5:
                          n.Dodge = e.bool();
                          break;
                        case 6:
                          n.Die = e.bool();
                          break;
                        case 7:
                          if (
                            ((n.Pos && n.Pos.length) || (n.Pos = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.Pos.push(e.int32());
                          else n.Pos.push(e.int32());
                          break;
                        case 8:
                          n.Knockback = e.int32();
                          break;
                        case 9:
                          n.AppendBuff = y.msg.BattleBuff.decode(e, e.uint32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.CrtSN &&
                      e.hasOwnProperty("CrtSN") &&
                      !f.isInteger(e.CrtSN)
                    )
                      return "CrtSN: integer expected";
                    if (
                      null != e.Dmg &&
                      e.hasOwnProperty("Dmg") &&
                      !f.isInteger(e.Dmg)
                    )
                      return "Dmg: integer expected";
                    if (
                      null != e.Critical &&
                      e.hasOwnProperty("Critical") &&
                      "boolean" != typeof e.Critical
                    )
                      return "Critical: boolean expected";
                    if (
                      null != e.CriticalRand &&
                      e.hasOwnProperty("CriticalRand") &&
                      !f.isInteger(e.CriticalRand)
                    )
                      return "CriticalRand: integer expected";
                    if (
                      null != e.Dodge &&
                      e.hasOwnProperty("Dodge") &&
                      "boolean" != typeof e.Dodge
                    )
                      return "Dodge: boolean expected";
                    if (
                      null != e.Die &&
                      e.hasOwnProperty("Die") &&
                      "boolean" != typeof e.Die
                    )
                      return "Die: boolean expected";
                    if (null != e.Pos && e.hasOwnProperty("Pos")) {
                      if (!Array.isArray(e.Pos)) return "Pos: array expected";
                      for (var t = 0; t < e.Pos.length; ++t)
                        if (!f.isInteger(e.Pos[t]))
                          return "Pos: integer[] expected";
                    }
                    if (
                      null != e.Knockback &&
                      e.hasOwnProperty("Knockback") &&
                      !f.isInteger(e.Knockback)
                    )
                      return "Knockback: integer expected";
                    if (
                      null != e.AppendBuff &&
                      e.hasOwnProperty("AppendBuff")
                    ) {
                      var r = y.msg.BattleBuff.verify(e.AppendBuff);
                      if (r) return "AppendBuff." + r;
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleDmg) return e;
                    var t = new y.msg.BattleDmg();
                    if (
                      (null != e.CrtSN && (t.CrtSN = 0 | e.CrtSN),
                      null != e.Dmg && (t.Dmg = 0 | e.Dmg),
                      null != e.Critical && (t.Critical = Boolean(e.Critical)),
                      null != e.CriticalRand &&
                        (t.CriticalRand = 0 | e.CriticalRand),
                      null != e.Dodge && (t.Dodge = Boolean(e.Dodge)),
                      null != e.Die && (t.Die = Boolean(e.Die)),
                      e.Pos)
                    ) {
                      if (!Array.isArray(e.Pos))
                        throw TypeError(".msg.BattleDmg.Pos: array expected");
                      t.Pos = [];
                      for (var r = 0; r < e.Pos.length; ++r)
                        t.Pos[r] = 0 | e.Pos[r];
                    }
                    if (
                      (null != e.Knockback && (t.Knockback = 0 | e.Knockback),
                      null != e.AppendBuff)
                    ) {
                      if ("object" != typeof e.AppendBuff)
                        throw TypeError(
                          ".msg.BattleDmg.AppendBuff: object expected"
                        );
                      t.AppendBuff = y.msg.BattleBuff.fromObject(e.AppendBuff);
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.Pos = []),
                      t.defaults &&
                        ((r.CrtSN = 0),
                        (r.Dmg = 0),
                        (r.Critical = !1),
                        (r.CriticalRand = 0),
                        (r.Dodge = !1),
                        (r.Die = !1),
                        (r.Knockback = 0),
                        (r.AppendBuff = null)),
                      null != e.CrtSN &&
                        e.hasOwnProperty("CrtSN") &&
                        (r.CrtSN = e.CrtSN),
                      null != e.Dmg &&
                        e.hasOwnProperty("Dmg") &&
                        (r.Dmg = e.Dmg),
                      null != e.Critical &&
                        e.hasOwnProperty("Critical") &&
                        (r.Critical = e.Critical),
                      null != e.CriticalRand &&
                        e.hasOwnProperty("CriticalRand") &&
                        (r.CriticalRand = e.CriticalRand),
                      null != e.Dodge &&
                        e.hasOwnProperty("Dodge") &&
                        (r.Dodge = e.Dodge),
                      null != e.Die &&
                        e.hasOwnProperty("Die") &&
                        (r.Die = e.Die),
                      e.Pos && e.Pos.length)
                    ) {
                      r.Pos = [];
                      for (var n = 0; n < e.Pos.length; ++n)
                        r.Pos[n] = e.Pos[n];
                    }
                    return (
                      null != e.Knockback &&
                        e.hasOwnProperty("Knockback") &&
                        (r.Knockback = e.Knockback),
                      null != e.AppendBuff &&
                        e.hasOwnProperty("AppendBuff") &&
                        (r.AppendBuff = y.msg.BattleBuff.toObject(
                          e.AppendBuff,
                          t
                        )),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleDmgEx = (function () {
                function e(e) {
                  if (((this.TypeVals = []), (this.Pos = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.TypeVals = f.emptyArray),
                  (e.prototype.MSN = 0),
                  (e.prototype.Pos = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.TypeVals && e.TypeVals.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.TypeVals.length; ++r)
                        t.int32(e.TypeVals[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.MSN &&
                        Object.hasOwnProperty.call(e, "MSN") &&
                        t.uint32(24).int32(e.MSN),
                      null != e.Pos && e.Pos.length)
                    ) {
                      for (t.uint32(34).fork(), r = 0; r < e.Pos.length; ++r)
                        t.int32(e.Pos[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleDmgEx();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.TypeVals && n.TypeVals.length) ||
                              (n.TypeVals = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.TypeVals.push(e.int32());
                          else n.TypeVals.push(e.int32());
                          break;
                        case 3:
                          n.MSN = e.int32();
                          break;
                        case 4:
                          if (
                            ((n.Pos && n.Pos.length) || (n.Pos = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.Pos.push(e.int32());
                          else n.Pos.push(e.int32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Type &&
                      e.hasOwnProperty("Type") &&
                      !f.isInteger(e.Type)
                    )
                      return "Type: integer expected";
                    if (null != e.TypeVals && e.hasOwnProperty("TypeVals")) {
                      if (!Array.isArray(e.TypeVals))
                        return "TypeVals: array expected";
                      for (var t = 0; t < e.TypeVals.length; ++t)
                        if (!f.isInteger(e.TypeVals[t]))
                          return "TypeVals: integer[] expected";
                    }
                    if (
                      null != e.MSN &&
                      e.hasOwnProperty("MSN") &&
                      !f.isInteger(e.MSN)
                    )
                      return "MSN: integer expected";
                    if (null != e.Pos && e.hasOwnProperty("Pos")) {
                      if (!Array.isArray(e.Pos)) return "Pos: array expected";
                      for (t = 0; t < e.Pos.length; ++t)
                        if (!f.isInteger(e.Pos[t]))
                          return "Pos: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleDmgEx) return e;
                    var t = new y.msg.BattleDmgEx();
                    if ((null != e.Type && (t.Type = 0 | e.Type), e.TypeVals)) {
                      if (!Array.isArray(e.TypeVals))
                        throw TypeError(
                          ".msg.BattleDmgEx.TypeVals: array expected"
                        );
                      t.TypeVals = [];
                      for (var r = 0; r < e.TypeVals.length; ++r)
                        t.TypeVals[r] = 0 | e.TypeVals[r];
                    }
                    if ((null != e.MSN && (t.MSN = 0 | e.MSN), e.Pos)) {
                      if (!Array.isArray(e.Pos))
                        throw TypeError(".msg.BattleDmgEx.Pos: array expected");
                      for (t.Pos = [], r = 0; r < e.Pos.length; ++r)
                        t.Pos[r] = 0 | e.Pos[r];
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.TypeVals = []), (r.Pos = [])),
                      t.defaults && ((r.Type = 0), (r.MSN = 0)),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      e.TypeVals && e.TypeVals.length)
                    ) {
                      r.TypeVals = [];
                      for (var n = 0; n < e.TypeVals.length; ++n)
                        r.TypeVals[n] = e.TypeVals[n];
                    }
                    if (
                      (null != e.MSN &&
                        e.hasOwnProperty("MSN") &&
                        (r.MSN = e.MSN),
                      e.Pos && e.Pos.length)
                    )
                      for (r.Pos = [], n = 0; n < e.Pos.length; ++n)
                        r.Pos[n] = e.Pos[n];
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleHeal = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.SN = 0),
                  (e.prototype.Heal = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.SN &&
                        Object.hasOwnProperty.call(e, "SN") &&
                        t.uint32(8).int32(e.SN),
                      null != e.Heal &&
                        Object.hasOwnProperty.call(e, "Heal") &&
                        t.uint32(16).int32(e.Heal),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleHeal();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.SN = e.int32();
                          break;
                        case 2:
                          n.Heal = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.SN &&
                        e.hasOwnProperty("SN") &&
                        !f.isInteger(e.SN)
                      ? "SN: integer expected"
                      : null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        !f.isInteger(e.Heal)
                      ? "Heal: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleHeal) return e;
                    var t = new y.msg.BattleHeal();
                    return (
                      null != e.SN && (t.SN = 0 | e.SN),
                      null != e.Heal && (t.Heal = 0 | e.Heal),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults && ((r.SN = 0), (r.Heal = 0)),
                      null != e.SN && e.hasOwnProperty("SN") && (r.SN = e.SN),
                      null != e.Heal &&
                        e.hasOwnProperty("Heal") &&
                        (r.Heal = e.Heal),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleBuff = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.ID = 0),
                  (e.prototype.AOType = 0),
                  (e.prototype.AOSN = 0),
                  (e.prototype.AOID = 0),
                  (e.prototype.AOCrtSN = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.ID &&
                        Object.hasOwnProperty.call(e, "ID") &&
                        t.uint32(8).int32(e.ID),
                      null != e.AOType &&
                        Object.hasOwnProperty.call(e, "AOType") &&
                        t.uint32(16).int32(e.AOType),
                      null != e.AOSN &&
                        Object.hasOwnProperty.call(e, "AOSN") &&
                        t.uint32(24).int32(e.AOSN),
                      null != e.AOID &&
                        Object.hasOwnProperty.call(e, "AOID") &&
                        t.uint32(32).int32(e.AOID),
                      null != e.AOCrtSN &&
                        Object.hasOwnProperty.call(e, "AOCrtSN") &&
                        t.uint32(40).int32(e.AOCrtSN),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleBuff();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.ID = e.int32();
                          break;
                        case 2:
                          n.AOType = e.int32();
                          break;
                        case 3:
                          n.AOSN = e.int32();
                          break;
                        case 4:
                          n.AOID = e.int32();
                          break;
                        case 5:
                          n.AOCrtSN = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.ID &&
                        e.hasOwnProperty("ID") &&
                        !f.isInteger(e.ID)
                      ? "ID: integer expected"
                      : null != e.AOType &&
                        e.hasOwnProperty("AOType") &&
                        !f.isInteger(e.AOType)
                      ? "AOType: integer expected"
                      : null != e.AOSN &&
                        e.hasOwnProperty("AOSN") &&
                        !f.isInteger(e.AOSN)
                      ? "AOSN: integer expected"
                      : null != e.AOID &&
                        e.hasOwnProperty("AOID") &&
                        !f.isInteger(e.AOID)
                      ? "AOID: integer expected"
                      : null != e.AOCrtSN &&
                        e.hasOwnProperty("AOCrtSN") &&
                        !f.isInteger(e.AOCrtSN)
                      ? "AOCrtSN: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleBuff) return e;
                    var t = new y.msg.BattleBuff();
                    return (
                      null != e.ID && (t.ID = 0 | e.ID),
                      null != e.AOType && (t.AOType = 0 | e.AOType),
                      null != e.AOSN && (t.AOSN = 0 | e.AOSN),
                      null != e.AOID && (t.AOID = 0 | e.AOID),
                      null != e.AOCrtSN && (t.AOCrtSN = 0 | e.AOCrtSN),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.ID = 0),
                        (r.AOType = 0),
                        (r.AOSN = 0),
                        (r.AOID = 0),
                        (r.AOCrtSN = 0)),
                      null != e.ID && e.hasOwnProperty("ID") && (r.ID = e.ID),
                      null != e.AOType &&
                        e.hasOwnProperty("AOType") &&
                        (r.AOType = e.AOType),
                      null != e.AOSN &&
                        e.hasOwnProperty("AOSN") &&
                        (r.AOSN = e.AOSN),
                      null != e.AOID &&
                        e.hasOwnProperty("AOID") &&
                        (r.AOID = e.AOID),
                      null != e.AOCrtSN &&
                        e.hasOwnProperty("AOCrtSN") &&
                        (r.AOCrtSN = e.AOCrtSN),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleMonsterAtk = (function () {
                function e(e) {
                  if (((this.MInfo = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.MInfo = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.MInfo && e.MInfo.length)
                    ) {
                      t.uint32(18).fork();
                      for (var r = 0; r < e.MInfo.length; ++r)
                        t.int32(e.MInfo[r]);
                      t.ldelim();
                    }
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleMonsterAtk();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          if (
                            ((n.MInfo && n.MInfo.length) || (n.MInfo = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.MInfo.push(e.int32());
                          else n.MInfo.push(e.int32());
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.Type &&
                      e.hasOwnProperty("Type") &&
                      !f.isInteger(e.Type)
                    )
                      return "Type: integer expected";
                    if (null != e.MInfo && e.hasOwnProperty("MInfo")) {
                      if (!Array.isArray(e.MInfo))
                        return "MInfo: array expected";
                      for (var t = 0; t < e.MInfo.length; ++t)
                        if (!f.isInteger(e.MInfo[t]))
                          return "MInfo: integer[] expected";
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleMonsterAtk) return e;
                    var t = new y.msg.BattleMonsterAtk();
                    if ((null != e.Type && (t.Type = 0 | e.Type), e.MInfo)) {
                      if (!Array.isArray(e.MInfo))
                        throw TypeError(
                          ".msg.BattleMonsterAtk.MInfo: array expected"
                        );
                      t.MInfo = [];
                      for (var r = 0; r < e.MInfo.length; ++r)
                        t.MInfo[r] = 0 | e.MInfo[r];
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.MInfo = []),
                      t.defaults && (r.Type = 0),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      e.MInfo && e.MInfo.length)
                    ) {
                      r.MInfo = [];
                      for (var n = 0; n < e.MInfo.length; ++n)
                        r.MInfo[n] = e.MInfo[n];
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleShop = (function () {
                function e(e) {
                  if (((this.Goods = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.ChestItem = 0),
                  (e.prototype.ChestNum = 0),
                  (e.prototype.Refresh = 0),
                  (e.prototype.RefreshFree = 0),
                  (e.prototype.RefreshGold = 0),
                  (e.prototype.Goods = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.ChestItem &&
                        Object.hasOwnProperty.call(e, "ChestItem") &&
                        t.uint32(8).int32(e.ChestItem),
                      null != e.ChestNum &&
                        Object.hasOwnProperty.call(e, "ChestNum") &&
                        t.uint32(16).int32(e.ChestNum),
                      null != e.Refresh &&
                        Object.hasOwnProperty.call(e, "Refresh") &&
                        t.uint32(24).int32(e.Refresh),
                      null != e.RefreshFree &&
                        Object.hasOwnProperty.call(e, "RefreshFree") &&
                        t.uint32(32).int32(e.RefreshFree),
                      null != e.RefreshGold &&
                        Object.hasOwnProperty.call(e, "RefreshGold") &&
                        t.uint32(40).int32(e.RefreshGold),
                      null != e.Goods && e.Goods.length)
                    )
                      for (var r = 0; r < e.Goods.length; ++r)
                        y.msg.BattleShopGoods.encode(
                          e.Goods[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleShop();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.ChestItem = e.int32();
                          break;
                        case 2:
                          n.ChestNum = e.int32();
                          break;
                        case 3:
                          n.Refresh = e.int32();
                          break;
                        case 4:
                          n.RefreshFree = e.int32();
                          break;
                        case 5:
                          n.RefreshGold = e.int32();
                          break;
                        case 6:
                          (n.Goods && n.Goods.length) || (n.Goods = []),
                            n.Goods.push(
                              y.msg.BattleShopGoods.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.ChestItem &&
                      e.hasOwnProperty("ChestItem") &&
                      !f.isInteger(e.ChestItem)
                    )
                      return "ChestItem: integer expected";
                    if (
                      null != e.ChestNum &&
                      e.hasOwnProperty("ChestNum") &&
                      !f.isInteger(e.ChestNum)
                    )
                      return "ChestNum: integer expected";
                    if (
                      null != e.Refresh &&
                      e.hasOwnProperty("Refresh") &&
                      !f.isInteger(e.Refresh)
                    )
                      return "Refresh: integer expected";
                    if (
                      null != e.RefreshFree &&
                      e.hasOwnProperty("RefreshFree") &&
                      !f.isInteger(e.RefreshFree)
                    )
                      return "RefreshFree: integer expected";
                    if (
                      null != e.RefreshGold &&
                      e.hasOwnProperty("RefreshGold") &&
                      !f.isInteger(e.RefreshGold)
                    )
                      return "RefreshGold: integer expected";
                    if (null != e.Goods && e.hasOwnProperty("Goods")) {
                      if (!Array.isArray(e.Goods))
                        return "Goods: array expected";
                      for (var t = 0; t < e.Goods.length; ++t) {
                        var r = y.msg.BattleShopGoods.verify(e.Goods[t]);
                        if (r) return "Goods." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleShop) return e;
                    var t = new y.msg.BattleShop();
                    if (
                      (null != e.ChestItem && (t.ChestItem = 0 | e.ChestItem),
                      null != e.ChestNum && (t.ChestNum = 0 | e.ChestNum),
                      null != e.Refresh && (t.Refresh = 0 | e.Refresh),
                      null != e.RefreshFree &&
                        (t.RefreshFree = 0 | e.RefreshFree),
                      null != e.RefreshGold &&
                        (t.RefreshGold = 0 | e.RefreshGold),
                      e.Goods)
                    ) {
                      if (!Array.isArray(e.Goods))
                        throw TypeError(
                          ".msg.BattleShop.Goods: array expected"
                        );
                      t.Goods = [];
                      for (var r = 0; r < e.Goods.length; ++r) {
                        if ("object" != typeof e.Goods[r])
                          throw TypeError(
                            ".msg.BattleShop.Goods: object expected"
                          );
                        t.Goods[r] = y.msg.BattleShopGoods.fromObject(
                          e.Goods[r]
                        );
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) && (r.Goods = []),
                      t.defaults &&
                        ((r.ChestItem = 0),
                        (r.ChestNum = 0),
                        (r.Refresh = 0),
                        (r.RefreshFree = 0),
                        (r.RefreshGold = 0)),
                      null != e.ChestItem &&
                        e.hasOwnProperty("ChestItem") &&
                        (r.ChestItem = e.ChestItem),
                      null != e.ChestNum &&
                        e.hasOwnProperty("ChestNum") &&
                        (r.ChestNum = e.ChestNum),
                      null != e.Refresh &&
                        e.hasOwnProperty("Refresh") &&
                        (r.Refresh = e.Refresh),
                      null != e.RefreshFree &&
                        e.hasOwnProperty("RefreshFree") &&
                        (r.RefreshFree = e.RefreshFree),
                      null != e.RefreshGold &&
                        e.hasOwnProperty("RefreshGold") &&
                        (r.RefreshGold = e.RefreshGold),
                      e.Goods && e.Goods.length)
                    ) {
                      r.Goods = [];
                      for (var n = 0; n < e.Goods.length; ++n)
                        r.Goods[n] = y.msg.BattleShopGoods.toObject(
                          e.Goods[n],
                          t
                        );
                    }
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleShopGoods = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.TblID = 0),
                  (e.prototype.Price = 0),
                  (e.prototype.RawPrice = 0),
                  (e.prototype.Lock = !1),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.TblID &&
                        Object.hasOwnProperty.call(e, "TblID") &&
                        t.uint32(16).int32(e.TblID),
                      null != e.Price &&
                        Object.hasOwnProperty.call(e, "Price") &&
                        t.uint32(24).int32(e.Price),
                      null != e.RawPrice &&
                        Object.hasOwnProperty.call(e, "RawPrice") &&
                        t.uint32(32).int32(e.RawPrice),
                      null != e.Lock &&
                        Object.hasOwnProperty.call(e, "Lock") &&
                        t.uint32(40).bool(e.Lock),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleShopGoods();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          n.TblID = e.int32();
                          break;
                        case 3:
                          n.Price = e.int32();
                          break;
                        case 4:
                          n.RawPrice = e.int32();
                          break;
                        case 5:
                          n.Lock = e.bool();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    return "object" != typeof e || null === e
                      ? "object expected"
                      : null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        !f.isInteger(e.Type)
                      ? "Type: integer expected"
                      : null != e.TblID &&
                        e.hasOwnProperty("TblID") &&
                        !f.isInteger(e.TblID)
                      ? "TblID: integer expected"
                      : null != e.Price &&
                        e.hasOwnProperty("Price") &&
                        !f.isInteger(e.Price)
                      ? "Price: integer expected"
                      : null != e.RawPrice &&
                        e.hasOwnProperty("RawPrice") &&
                        !f.isInteger(e.RawPrice)
                      ? "RawPrice: integer expected"
                      : null != e.Lock &&
                        e.hasOwnProperty("Lock") &&
                        "boolean" != typeof e.Lock
                      ? "Lock: boolean expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleShopGoods) return e;
                    var t = new y.msg.BattleShopGoods();
                    return (
                      null != e.Type && (t.Type = 0 | e.Type),
                      null != e.TblID && (t.TblID = 0 | e.TblID),
                      null != e.Price && (t.Price = 0 | e.Price),
                      null != e.RawPrice && (t.RawPrice = 0 | e.RawPrice),
                      null != e.Lock && (t.Lock = Boolean(e.Lock)),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.Type = 0),
                        (r.TblID = 0),
                        (r.Price = 0),
                        (r.RawPrice = 0),
                        (r.Lock = !1)),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type = e.Type),
                      null != e.TblID &&
                        e.hasOwnProperty("TblID") &&
                        (r.TblID = e.TblID),
                      null != e.Price &&
                        e.hasOwnProperty("Price") &&
                        (r.Price = e.Price),
                      null != e.RawPrice &&
                        e.hasOwnProperty("RawPrice") &&
                        (r.RawPrice = e.RawPrice),
                      null != e.Lock &&
                        e.hasOwnProperty("Lock") &&
                        (r.Lock = e.Lock),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleTileItem = (function () {
                function e(e) {
                  if (e)
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Type = 0),
                  (e.prototype.X = 0),
                  (e.prototype.Y = 0),
                  (e.prototype.Val = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    return (
                      t || (t = p.create()),
                      null != e.Type &&
                        Object.hasOwnProperty.call(e, "Type") &&
                        t.uint32(8).int32(e.Type),
                      null != e.X &&
                        Object.hasOwnProperty.call(e, "X") &&
                        t.uint32(16).int32(e.X),
                      null != e.Y &&
                        Object.hasOwnProperty.call(e, "Y") &&
                        t.uint32(24).int32(e.Y),
                      null != e.Val &&
                        Object.hasOwnProperty.call(e, "Val") &&
                        t.uint32(32).int32(e.Val),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleTileItem();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.Type = e.int32();
                          break;
                        case 2:
                          n.X = e.int32();
                          break;
                        case 3:
                          n.Y = e.int32();
                          break;
                        case 4:
                          n.Val = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Type && e.hasOwnProperty("Type"))
                      switch (e.Type) {
                        default:
                          return "Type: enum value expected";
                        case 0:
                        case 1:
                        case 2:
                      }
                    return null != e.X &&
                      e.hasOwnProperty("X") &&
                      !f.isInteger(e.X)
                      ? "X: integer expected"
                      : null != e.Y &&
                        e.hasOwnProperty("Y") &&
                        !f.isInteger(e.Y)
                      ? "Y: integer expected"
                      : null != e.Val &&
                        e.hasOwnProperty("Val") &&
                        !f.isInteger(e.Val)
                      ? "Val: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleTileItem) return e;
                    var t = new y.msg.BattleTileItem();
                    switch (e.Type) {
                      case "BTI_Coin":
                      case 0:
                        t.Type = 0;
                        break;
                      case "BTI_Chest":
                      case 1:
                        t.Type = 1;
                        break;
                      case "BTI_Potion":
                      case 2:
                        t.Type = 2;
                    }
                    return (
                      null != e.X && (t.X = 0 | e.X),
                      null != e.Y && (t.Y = 0 | e.Y),
                      null != e.Val && (t.Val = 0 | e.Val),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    return (
                      t.defaults &&
                        ((r.Type = t.enums === String ? "BTI_Coin" : 0),
                        (r.X = 0),
                        (r.Y = 0),
                        (r.Val = 0)),
                      null != e.Type &&
                        e.hasOwnProperty("Type") &&
                        (r.Type =
                          t.enums === String
                            ? y.msg.BattleTileItemType[e.Type]
                            : e.Type),
                      null != e.X && e.hasOwnProperty("X") && (r.X = e.X),
                      null != e.Y && e.hasOwnProperty("Y") && (r.Y = e.Y),
                      null != e.Val &&
                        e.hasOwnProperty("Val") &&
                        (r.Val = e.Val),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.DropChest = (function () {
                function e(e) {
                  if (((this.ETokens = []), (this.MTokens = []), e))
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.DropID = 0),
                  (e.prototype.C1 = ""),
                  (e.prototype.C2 = ""),
                  (e.prototype.ETokens = f.emptyArray),
                  (e.prototype.MTokens = f.emptyArray),
                  (e.prototype.X = 0),
                  (e.prototype.Y = 0),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.DropID &&
                        Object.hasOwnProperty.call(e, "DropID") &&
                        t.uint32(8).int32(e.DropID),
                      null != e.C1 &&
                        Object.hasOwnProperty.call(e, "C1") &&
                        t.uint32(26).string(e.C1),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(34).string(e.C2),
                      null != e.ETokens && e.ETokens.length)
                    )
                      for (var r = 0; r < e.ETokens.length; ++r)
                        y.msg.EquipmentToken.encode(
                          e.ETokens[r],
                          t.uint32(42).fork()
                        ).ldelim();
                    if (null != e.MTokens && e.MTokens.length)
                      for (r = 0; r < e.MTokens.length; ++r)
                        y.msg.MaterialToken.encode(
                          e.MTokens[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return (
                      null != e.X &&
                        Object.hasOwnProperty.call(e, "X") &&
                        t.uint32(56).int32(e.X),
                      null != e.Y &&
                        Object.hasOwnProperty.call(e, "Y") &&
                        t.uint32(64).int32(e.Y),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.DropChest();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          n.DropID = e.int32();
                          break;
                        case 3:
                          n.C1 = e.string();
                          break;
                        case 4:
                          n.C2 = e.string();
                          break;
                        case 5:
                          (n.ETokens && n.ETokens.length) || (n.ETokens = []),
                            n.ETokens.push(
                              y.msg.EquipmentToken.decode(e, e.uint32())
                            );
                          break;
                        case 6:
                          (n.MTokens && n.MTokens.length) || (n.MTokens = []),
                            n.MTokens.push(
                              y.msg.MaterialToken.decode(e, e.uint32())
                            );
                          break;
                        case 7:
                          n.X = e.int32();
                          break;
                        case 8:
                          n.Y = e.int32();
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (
                      null != e.DropID &&
                      e.hasOwnProperty("DropID") &&
                      !f.isInteger(e.DropID)
                    )
                      return "DropID: integer expected";
                    if (
                      null != e.C1 &&
                      e.hasOwnProperty("C1") &&
                      !f.isString(e.C1)
                    )
                      return "C1: string expected";
                    if (
                      null != e.C2 &&
                      e.hasOwnProperty("C2") &&
                      !f.isString(e.C2)
                    )
                      return "C2: string expected";
                    if (null != e.ETokens && e.hasOwnProperty("ETokens")) {
                      if (!Array.isArray(e.ETokens))
                        return "ETokens: array expected";
                      for (var t = 0; t < e.ETokens.length; ++t)
                        if ((r = y.msg.EquipmentToken.verify(e.ETokens[t])))
                          return "ETokens." + r;
                    }
                    if (null != e.MTokens && e.hasOwnProperty("MTokens")) {
                      if (!Array.isArray(e.MTokens))
                        return "MTokens: array expected";
                      for (t = 0; t < e.MTokens.length; ++t) {
                        var r;
                        if ((r = y.msg.MaterialToken.verify(e.MTokens[t])))
                          return "MTokens." + r;
                      }
                    }
                    return null != e.X &&
                      e.hasOwnProperty("X") &&
                      !f.isInteger(e.X)
                      ? "X: integer expected"
                      : null != e.Y &&
                        e.hasOwnProperty("Y") &&
                        !f.isInteger(e.Y)
                      ? "Y: integer expected"
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.DropChest) return e;
                    var t = new y.msg.DropChest();
                    if (
                      (null != e.DropID && (t.DropID = 0 | e.DropID),
                      null != e.C1 && (t.C1 = String(e.C1)),
                      null != e.C2 && (t.C2 = String(e.C2)),
                      e.ETokens)
                    ) {
                      if (!Array.isArray(e.ETokens))
                        throw TypeError(
                          ".msg.DropChest.ETokens: array expected"
                        );
                      t.ETokens = [];
                      for (var r = 0; r < e.ETokens.length; ++r) {
                        if ("object" != typeof e.ETokens[r])
                          throw TypeError(
                            ".msg.DropChest.ETokens: object expected"
                          );
                        t.ETokens[r] = y.msg.EquipmentToken.fromObject(
                          e.ETokens[r]
                        );
                      }
                    }
                    if (e.MTokens) {
                      if (!Array.isArray(e.MTokens))
                        throw TypeError(
                          ".msg.DropChest.MTokens: array expected"
                        );
                      for (t.MTokens = [], r = 0; r < e.MTokens.length; ++r) {
                        if ("object" != typeof e.MTokens[r])
                          throw TypeError(
                            ".msg.DropChest.MTokens: object expected"
                          );
                        t.MTokens[r] = y.msg.MaterialToken.fromObject(
                          e.MTokens[r]
                        );
                      }
                    }
                    return (
                      null != e.X && (t.X = 0 | e.X),
                      null != e.Y && (t.Y = 0 | e.Y),
                      t
                    );
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.ETokens = []), (r.MTokens = [])),
                      t.defaults &&
                        ((r.DropID = 0),
                        (r.C1 = ""),
                        (r.C2 = ""),
                        (r.X = 0),
                        (r.Y = 0)),
                      null != e.DropID &&
                        e.hasOwnProperty("DropID") &&
                        (r.DropID = e.DropID),
                      null != e.C1 && e.hasOwnProperty("C1") && (r.C1 = e.C1),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      e.ETokens && e.ETokens.length)
                    ) {
                      r.ETokens = [];
                      for (var n = 0; n < e.ETokens.length; ++n)
                        r.ETokens[n] = y.msg.EquipmentToken.toObject(
                          e.ETokens[n],
                          t
                        );
                    }
                    if (e.MTokens && e.MTokens.length)
                      for (r.MTokens = [], n = 0; n < e.MTokens.length; ++n)
                        r.MTokens[n] = y.msg.MaterialToken.toObject(
                          e.MTokens[n],
                          t
                        );
                    return (
                      null != e.X && e.hasOwnProperty("X") && (r.X = e.X),
                      null != e.Y && e.hasOwnProperty("Y") && (r.Y = e.Y),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.DropInfo = (function () {
                function e(e) {
                  if (
                    ((this.DropStage = []),
                    (this.DropID = []),
                    (this.ETokens = []),
                    (this.MTokens = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.DropStage = f.emptyArray),
                  (e.prototype.DropID = f.emptyArray),
                  (e.prototype.C1 = ""),
                  (e.prototype.C2 = ""),
                  (e.prototype.ETokens = f.emptyArray),
                  (e.prototype.MTokens = f.emptyArray),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()),
                      null != e.DropStage && e.DropStage.length)
                    ) {
                      t.uint32(10).fork();
                      for (var r = 0; r < e.DropStage.length; ++r)
                        t.int32(e.DropStage[r]);
                      t.ldelim();
                    }
                    if (null != e.DropID && e.DropID.length) {
                      for (t.uint32(18).fork(), r = 0; r < e.DropID.length; ++r)
                        t.int32(e.DropID[r]);
                      t.ldelim();
                    }
                    if (
                      (null != e.C1 &&
                        Object.hasOwnProperty.call(e, "C1") &&
                        t.uint32(26).string(e.C1),
                      null != e.C2 &&
                        Object.hasOwnProperty.call(e, "C2") &&
                        t.uint32(34).string(e.C2),
                      null != e.ETokens && e.ETokens.length)
                    )
                      for (r = 0; r < e.ETokens.length; ++r)
                        y.msg.EquipmentToken.encode(
                          e.ETokens[r],
                          t.uint32(42).fork()
                        ).ldelim();
                    if (null != e.MTokens && e.MTokens.length)
                      for (r = 0; r < e.MTokens.length; ++r)
                        y.msg.MaterialToken.encode(
                          e.MTokens[r],
                          t.uint32(50).fork()
                        ).ldelim();
                    return t;
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.DropInfo();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          if (
                            ((n.DropStage && n.DropStage.length) ||
                              (n.DropStage = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.DropStage.push(e.int32());
                          else n.DropStage.push(e.int32());
                          break;
                        case 2:
                          if (
                            ((n.DropID && n.DropID.length) || (n.DropID = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.DropID.push(e.int32());
                          else n.DropID.push(e.int32());
                          break;
                        case 3:
                          n.C1 = e.string();
                          break;
                        case 4:
                          n.C2 = e.string();
                          break;
                        case 5:
                          (n.ETokens && n.ETokens.length) || (n.ETokens = []),
                            n.ETokens.push(
                              y.msg.EquipmentToken.decode(e, e.uint32())
                            );
                          break;
                        case 6:
                          (n.MTokens && n.MTokens.length) || (n.MTokens = []),
                            n.MTokens.push(
                              y.msg.MaterialToken.decode(e, e.uint32())
                            );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.DropStage && e.hasOwnProperty("DropStage")) {
                      if (!Array.isArray(e.DropStage))
                        return "DropStage: array expected";
                      for (var t = 0; t < e.DropStage.length; ++t)
                        if (!f.isInteger(e.DropStage[t]))
                          return "DropStage: integer[] expected";
                    }
                    if (null != e.DropID && e.hasOwnProperty("DropID")) {
                      if (!Array.isArray(e.DropID))
                        return "DropID: array expected";
                      for (t = 0; t < e.DropID.length; ++t)
                        if (!f.isInteger(e.DropID[t]))
                          return "DropID: integer[] expected";
                    }
                    if (
                      null != e.C1 &&
                      e.hasOwnProperty("C1") &&
                      !f.isString(e.C1)
                    )
                      return "C1: string expected";
                    if (
                      null != e.C2 &&
                      e.hasOwnProperty("C2") &&
                      !f.isString(e.C2)
                    )
                      return "C2: string expected";
                    if (null != e.ETokens && e.hasOwnProperty("ETokens")) {
                      if (!Array.isArray(e.ETokens))
                        return "ETokens: array expected";
                      for (t = 0; t < e.ETokens.length; ++t)
                        if ((r = y.msg.EquipmentToken.verify(e.ETokens[t])))
                          return "ETokens." + r;
                    }
                    if (null != e.MTokens && e.hasOwnProperty("MTokens")) {
                      if (!Array.isArray(e.MTokens))
                        return "MTokens: array expected";
                      for (t = 0; t < e.MTokens.length; ++t) {
                        var r;
                        if ((r = y.msg.MaterialToken.verify(e.MTokens[t])))
                          return "MTokens." + r;
                      }
                    }
                    return null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.DropInfo) return e;
                    var t = new y.msg.DropInfo();
                    if (e.DropStage) {
                      if (!Array.isArray(e.DropStage))
                        throw TypeError(
                          ".msg.DropInfo.DropStage: array expected"
                        );
                      t.DropStage = [];
                      for (var r = 0; r < e.DropStage.length; ++r)
                        t.DropStage[r] = 0 | e.DropStage[r];
                    }
                    if (e.DropID) {
                      if (!Array.isArray(e.DropID))
                        throw TypeError(".msg.DropInfo.DropID: array expected");
                      for (t.DropID = [], r = 0; r < e.DropID.length; ++r)
                        t.DropID[r] = 0 | e.DropID[r];
                    }
                    if (
                      (null != e.C1 && (t.C1 = String(e.C1)),
                      null != e.C2 && (t.C2 = String(e.C2)),
                      e.ETokens)
                    ) {
                      if (!Array.isArray(e.ETokens))
                        throw TypeError(
                          ".msg.DropInfo.ETokens: array expected"
                        );
                      for (t.ETokens = [], r = 0; r < e.ETokens.length; ++r) {
                        if ("object" != typeof e.ETokens[r])
                          throw TypeError(
                            ".msg.DropInfo.ETokens: object expected"
                          );
                        t.ETokens[r] = y.msg.EquipmentToken.fromObject(
                          e.ETokens[r]
                        );
                      }
                    }
                    if (e.MTokens) {
                      if (!Array.isArray(e.MTokens))
                        throw TypeError(
                          ".msg.DropInfo.MTokens: array expected"
                        );
                      for (t.MTokens = [], r = 0; r < e.MTokens.length; ++r) {
                        if ("object" != typeof e.MTokens[r])
                          throw TypeError(
                            ".msg.DropInfo.MTokens: object expected"
                          );
                        t.MTokens[r] = y.msg.MaterialToken.fromObject(
                          e.MTokens[r]
                        );
                      }
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.DropStage = []),
                        (r.DropID = []),
                        (r.ETokens = []),
                        (r.MTokens = [])),
                      t.defaults && ((r.C1 = ""), (r.C2 = "")),
                      e.DropStage && e.DropStage.length)
                    ) {
                      r.DropStage = [];
                      for (var n = 0; n < e.DropStage.length; ++n)
                        r.DropStage[n] = e.DropStage[n];
                    }
                    if (e.DropID && e.DropID.length)
                      for (r.DropID = [], n = 0; n < e.DropID.length; ++n)
                        r.DropID[n] = e.DropID[n];
                    if (
                      (null != e.C1 && e.hasOwnProperty("C1") && (r.C1 = e.C1),
                      null != e.C2 && e.hasOwnProperty("C2") && (r.C2 = e.C2),
                      e.ETokens && e.ETokens.length)
                    )
                      for (r.ETokens = [], n = 0; n < e.ETokens.length; ++n)
                        r.ETokens[n] = y.msg.EquipmentToken.toObject(
                          e.ETokens[n],
                          t
                        );
                    if (e.MTokens && e.MTokens.length)
                      for (r.MTokens = [], n = 0; n < e.MTokens.length; ++n)
                        r.MTokens[n] = y.msg.MaterialToken.toObject(
                          e.MTokens[n],
                          t
                        );
                    return r;
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              (s.BattleUpdate = (function () {
                function e(e) {
                  if (
                    ((this.Items = []),
                    (this.WDel = []),
                    (this.WAdd = []),
                    (this.WUpdate = []),
                    e)
                  )
                    for (var t = Object.keys(e), r = 0; r < t.length; ++r)
                      null != e[t[r]] && (this[t[r]] = e[t[r]]);
                }
                return (
                  (e.prototype.Items = f.emptyArray),
                  (e.prototype.WDel = f.emptyArray),
                  (e.prototype.WAdd = f.emptyArray),
                  (e.prototype.WUpdate = f.emptyArray),
                  (e.prototype.CharAttrUpdate = null),
                  (e.create = function (t) {
                    return new e(t);
                  }),
                  (e.encode = function (e, t) {
                    if (
                      (t || (t = p.create()), null != e.Items && e.Items.length)
                    ) {
                      t.uint32(10).fork();
                      for (var r = 0; r < e.Items.length; ++r)
                        t.int32(e.Items[r]);
                      t.ldelim();
                    }
                    if (null != e.WDel && e.WDel.length) {
                      for (t.uint32(18).fork(), r = 0; r < e.WDel.length; ++r)
                        t.int32(e.WDel[r]);
                      t.ldelim();
                    }
                    if (null != e.WAdd && e.WAdd.length)
                      for (r = 0; r < e.WAdd.length; ++r)
                        y.msg.BattleWeapon.encode(
                          e.WAdd[r],
                          t.uint32(26).fork()
                        ).ldelim();
                    if (null != e.WUpdate && e.WUpdate.length)
                      for (r = 0; r < e.WUpdate.length; ++r)
                        y.msg.BattleWeaponUpdate.encode(
                          e.WUpdate[r],
                          t.uint32(34).fork()
                        ).ldelim();
                    return (
                      null != e.CharAttrUpdate &&
                        Object.hasOwnProperty.call(e, "CharAttrUpdate") &&
                        y.msg.BattleAttrUpdate.encode(
                          e.CharAttrUpdate,
                          t.uint32(42).fork()
                        ).ldelim(),
                      t
                    );
                  }),
                  (e.encodeDelimited = function (e, t) {
                    return this.encode(e, t).ldelim();
                  }),
                  (e.decode = function (e, t) {
                    e instanceof u || (e = u.create(e));
                    for (
                      var r = void 0 === t ? e.len : e.pos + t,
                        n = new y.msg.BattleUpdate();
                      e.pos < r;

                    ) {
                      var o = e.uint32();
                      switch (o >>> 3) {
                        case 1:
                          if (
                            ((n.Items && n.Items.length) || (n.Items = []),
                            2 == (7 & o))
                          )
                            for (var i = e.uint32() + e.pos; e.pos < i; )
                              n.Items.push(e.int32());
                          else n.Items.push(e.int32());
                          break;
                        case 2:
                          if (
                            ((n.WDel && n.WDel.length) || (n.WDel = []),
                            2 == (7 & o))
                          )
                            for (i = e.uint32() + e.pos; e.pos < i; )
                              n.WDel.push(e.int32());
                          else n.WDel.push(e.int32());
                          break;
                        case 3:
                          (n.WAdd && n.WAdd.length) || (n.WAdd = []),
                            n.WAdd.push(
                              y.msg.BattleWeapon.decode(e, e.uint32())
                            );
                          break;
                        case 4:
                          (n.WUpdate && n.WUpdate.length) || (n.WUpdate = []),
                            n.WUpdate.push(
                              y.msg.BattleWeaponUpdate.decode(e, e.uint32())
                            );
                          break;
                        case 5:
                          n.CharAttrUpdate = y.msg.BattleAttrUpdate.decode(
                            e,
                            e.uint32()
                          );
                          break;
                        default:
                          e.skipType(7 & o);
                      }
                    }
                    return n;
                  }),
                  (e.decodeDelimited = function (e) {
                    return (
                      e instanceof u || (e = new u(e)),
                      this.decode(e, e.uint32())
                    );
                  }),
                  (e.verify = function (e) {
                    if ("object" != typeof e || null === e)
                      return "object expected";
                    if (null != e.Items && e.hasOwnProperty("Items")) {
                      if (!Array.isArray(e.Items))
                        return "Items: array expected";
                      for (var t = 0; t < e.Items.length; ++t)
                        if (!f.isInteger(e.Items[t]))
                          return "Items: integer[] expected";
                    }
                    if (null != e.WDel && e.hasOwnProperty("WDel")) {
                      if (!Array.isArray(e.WDel)) return "WDel: array expected";
                      for (t = 0; t < e.WDel.length; ++t)
                        if (!f.isInteger(e.WDel[t]))
                          return "WDel: integer[] expected";
                    }
                    if (null != e.WAdd && e.hasOwnProperty("WAdd")) {
                      if (!Array.isArray(e.WAdd)) return "WAdd: array expected";
                      for (t = 0; t < e.WAdd.length; ++t)
                        if ((r = y.msg.BattleWeapon.verify(e.WAdd[t])))
                          return "WAdd." + r;
                    }
                    if (null != e.WUpdate && e.hasOwnProperty("WUpdate")) {
                      if (!Array.isArray(e.WUpdate))
                        return "WUpdate: array expected";
                      for (t = 0; t < e.WUpdate.length; ++t)
                        if ((r = y.msg.BattleWeaponUpdate.verify(e.WUpdate[t])))
                          return "WUpdate." + r;
                    }
                    var r;
                    return null != e.CharAttrUpdate &&
                      e.hasOwnProperty("CharAttrUpdate") &&
                      (r = y.msg.BattleAttrUpdate.verify(e.CharAttrUpdate))
                      ? "CharAttrUpdate." + r
                      : null;
                  }),
                  (e.fromObject = function (e) {
                    if (e instanceof y.msg.BattleUpdate) return e;
                    var t = new y.msg.BattleUpdate();
                    if (e.Items) {
                      if (!Array.isArray(e.Items))
                        throw TypeError(
                          ".msg.BattleUpdate.Items: array expected"
                        );
                      t.Items = [];
                      for (var r = 0; r < e.Items.length; ++r)
                        t.Items[r] = 0 | e.Items[r];
                    }
                    if (e.WDel) {
                      if (!Array.isArray(e.WDel))
                        throw TypeError(
                          ".msg.BattleUpdate.WDel: array expected"
                        );
                      for (t.WDel = [], r = 0; r < e.WDel.length; ++r)
                        t.WDel[r] = 0 | e.WDel[r];
                    }
                    if (e.WAdd) {
                      if (!Array.isArray(e.WAdd))
                        throw TypeError(
                          ".msg.BattleUpdate.WAdd: array expected"
                        );
                      for (t.WAdd = [], r = 0; r < e.WAdd.length; ++r) {
                        if ("object" != typeof e.WAdd[r])
                          throw TypeError(
                            ".msg.BattleUpdate.WAdd: object expected"
                          );
                        t.WAdd[r] = y.msg.BattleWeapon.fromObject(e.WAdd[r]);
                      }
                    }
                    if (e.WUpdate) {
                      if (!Array.isArray(e.WUpdate))
                        throw TypeError(
                          ".msg.BattleUpdate.WUpdate: array expected"
                        );
                      for (t.WUpdate = [], r = 0; r < e.WUpdate.length; ++r) {
                        if ("object" != typeof e.WUpdate[r])
                          throw TypeError(
                            ".msg.BattleUpdate.WUpdate: object expected"
                          );
                        t.WUpdate[r] = y.msg.BattleWeaponUpdate.fromObject(
                          e.WUpdate[r]
                        );
                      }
                    }
                    if (null != e.CharAttrUpdate) {
                      if ("object" != typeof e.CharAttrUpdate)
                        throw TypeError(
                          ".msg.BattleUpdate.CharAttrUpdate: object expected"
                        );
                      t.CharAttrUpdate = y.msg.BattleAttrUpdate.fromObject(
                        e.CharAttrUpdate
                      );
                    }
                    return t;
                  }),
                  (e.toObject = function (e, t) {
                    t || (t = {});
                    var r = {};
                    if (
                      ((t.arrays || t.defaults) &&
                        ((r.Items = []),
                        (r.WDel = []),
                        (r.WAdd = []),
                        (r.WUpdate = [])),
                      t.defaults && (r.CharAttrUpdate = null),
                      e.Items && e.Items.length)
                    ) {
                      r.Items = [];
                      for (var n = 0; n < e.Items.length; ++n)
                        r.Items[n] = e.Items[n];
                    }
                    if (e.WDel && e.WDel.length)
                      for (r.WDel = [], n = 0; n < e.WDel.length; ++n)
                        r.WDel[n] = e.WDel[n];
                    if (e.WAdd && e.WAdd.length)
                      for (r.WAdd = [], n = 0; n < e.WAdd.length; ++n)
                        r.WAdd[n] = y.msg.BattleWeapon.toObject(e.WAdd[n], t);
                    if (e.WUpdate && e.WUpdate.length)
                      for (r.WUpdate = [], n = 0; n < e.WUpdate.length; ++n)
                        r.WUpdate[n] = y.msg.BattleWeaponUpdate.toObject(
                          e.WUpdate[n],
                          t
                        );
                    return (
                      null != e.CharAttrUpdate &&
                        e.hasOwnProperty("CharAttrUpdate") &&
                        (r.CharAttrUpdate = y.msg.BattleAttrUpdate.toObject(
                          e.CharAttrUpdate,
                          t
                        )),
                      r
                    );
                  }),
                  (e.prototype.toJSON = function () {
                    return this.constructor.toObject(
                      this,
                      c.util.toJSONOptions
                    );
                  }),
                  e
                );
              })()),
              s)),
              (n.exports = y),
              e("default", n.exports);
          },
          function () {
            return {
              "protobufjs/minimal.js": n,
            };
          }
        );
      },
    };
  }
);
