System.register(
  [process.cwd() + "/game_sources/cjs-loader.mjs"],
  function (exports, module) {
    var loader;
    return {
      setters: [
        function (e) {
          loader = e.default;
        },
      ],
      execute: function () {
        var __cjsMetaURL = exports("__cjsMetaURL", module.meta.url);
        loader.define(
          __cjsMetaURL,
          function (exports, require, module, __filename, __dirname) {
            function inquire(moduleName) {
              try {
                var mod = eval("quire".replace(/^/, "re"))(moduleName);
                if (mod && (mod.length || Object.keys(mod).length)) return mod;
              } catch (e) {}
              return null;
            }
            (module.exports = inquire), module.exports;
          },
          {}
        );
      },
    };
  }
);
