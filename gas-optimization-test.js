/**
 * Gas Optimization Test Script
 * 
 * This script demonstrates the gas fee optimizations implemented in CheckAir.js
 * Run this to see the difference between optimized and unoptimized settings.
 */

const { Connection, clusterApiUrl, ComputeBudgetProgram } = require("@solana/web3.js");

// Original settings (before optimization)
const ORIGINAL_SETTINGS = {
  COMPUTE_UNIT_LIMIT: 1_400_000,
  PRIORITY_FEE_MICROLAMPORTS: 1
};

// Optimized settings (after optimization)
const OPTIMIZED_SETTINGS = {
  COMPUTE_UNIT_LIMIT: 500_000,  // Reduced by ~64%
  PRIORITY_FEE_MICROLAMPORTS: 0  // Reduced to 0 for minimum cost
};

function calculateTransactionCost(computeUnits, priorityFeeMicroLamports) {
  // Base transaction fee is always 5000 lamports (0.000005 SOL)
  const baseFee = 5000;
  
  // Priority fee = compute units × priority fee per unit
  const priorityFee = computeUnits * priorityFeeMicroLamports;
  
  // Total fee in lamports
  const totalFeeLamports = baseFee + priorityFee;
  
  // Convert to SOL (1 SOL = 1,000,000,000 lamports)
  const totalFeeSOL = totalFeeLamports / 1_000_000_000;
  
  return {
    baseFee,
    priorityFee,
    totalFeeLamports,
    totalFeeSOL
  };
}

function compareGasCosts() {
  console.log("🔍 SOLANA GAS FEE OPTIMIZATION COMPARISON\n");
  
  // Calculate costs for original settings
  const originalCost = calculateTransactionCost(
    ORIGINAL_SETTINGS.COMPUTE_UNIT_LIMIT,
    ORIGINAL_SETTINGS.PRIORITY_FEE_MICROLAMPORTS
  );
  
  // Calculate costs for optimized settings
  const optimizedCost = calculateTransactionCost(
    OPTIMIZED_SETTINGS.COMPUTE_UNIT_LIMIT,
    OPTIMIZED_SETTINGS.PRIORITY_FEE_MICROLAMPORTS
  );
  
  console.log("📊 ORIGINAL SETTINGS:");
  console.log(`   Compute Unit Limit: ${ORIGINAL_SETTINGS.COMPUTE_UNIT_LIMIT.toLocaleString()}`);
  console.log(`   Priority Fee: ${ORIGINAL_SETTINGS.PRIORITY_FEE_MICROLAMPORTS} microLamports`);
  console.log(`   Total Cost: ${originalCost.totalFeeSOL.toFixed(9)} SOL`);
  console.log(`   Priority Fee Cost: ${(originalCost.priorityFee / 1_000_000_000).toFixed(9)} SOL`);
  
  console.log("\n✅ OPTIMIZED SETTINGS:");
  console.log(`   Compute Unit Limit: ${OPTIMIZED_SETTINGS.COMPUTE_UNIT_LIMIT.toLocaleString()}`);
  console.log(`   Priority Fee: ${OPTIMIZED_SETTINGS.PRIORITY_FEE_MICROLAMPORTS} microLamports`);
  console.log(`   Total Cost: ${optimizedCost.totalFeeSOL.toFixed(9)} SOL`);
  console.log(`   Priority Fee Cost: ${(optimizedCost.priorityFee / 1_000_000_000).toFixed(9)} SOL`);
  
  // Calculate savings
  const savingsLamports = originalCost.totalFeeLamports - optimizedCost.totalFeeLamports;
  const savingsSOL = originalCost.totalFeeSOL - optimizedCost.totalFeeSOL;
  const savingsPercentage = ((savingsLamports / originalCost.totalFeeLamports) * 100);
  
  console.log("\n💰 SAVINGS:");
  console.log(`   Absolute: ${savingsSOL.toFixed(9)} SOL (${savingsLamports.toLocaleString()} lamports)`);
  console.log(`   Percentage: ${savingsPercentage.toFixed(1)}%`);
  
  // Show cost for 100 transactions
  console.log("\n📈 COST FOR 100 TRANSACTIONS:");
  console.log(`   Original: ${(originalCost.totalFeeSOL * 100).toFixed(6)} SOL`);
  console.log(`   Optimized: ${(optimizedCost.totalFeeSOL * 100).toFixed(6)} SOL`);
  console.log(`   Savings: ${(savingsSOL * 100).toFixed(6)} SOL`);
}

function showNetworkConditionExamples() {
  console.log("\n\n🌐 NETWORK CONDITION EXAMPLES:\n");
  
  const scenarios = [
    { name: "Low Congestion", fee: 0, description: "Normal network conditions" },
    { name: "Normal Congestion", fee: 1_000, description: "Moderate network activity" },
    { name: "High Congestion", fee: 10_000, description: "Heavy network activity" },
    { name: "Urgent", fee: 50_000, description: "Critical transaction priority" }
  ];
  
  scenarios.forEach(scenario => {
    const cost = calculateTransactionCost(OPTIMIZED_SETTINGS.COMPUTE_UNIT_LIMIT, scenario.fee);
    console.log(`${scenario.name}:`);
    console.log(`   Priority Fee: ${scenario.fee.toLocaleString()} microLamports`);
    console.log(`   Total Cost: ${cost.totalFeeSOL.toFixed(9)} SOL`);
    console.log(`   Description: ${scenario.description}\n`);
  });
}

function showOptimizationFeatures() {
  console.log("🚀 OPTIMIZATION FEATURES IMPLEMENTED:\n");
  
  const features = [
    "✅ Transaction simulation to determine exact compute units needed",
    "✅ Dynamic compute unit limit (reduced from 1.4M to ~500K typical)",
    "✅ Zero priority fee by default for minimum cost",
    "✅ Network congestion monitoring and automatic adjustment",
    "✅ Retry logic with exponential priority fee increases",
    "✅ Comprehensive error handling and user guidance",
    "✅ Real-time cost calculation and reporting"
  ];
  
  features.forEach(feature => console.log(`   ${feature}`));
  
  console.log("\n📋 USAGE RECOMMENDATIONS:\n");
  console.log("   • Start with 0 microLamports priority fee for minimum cost");
  console.log("   • Let the system automatically adjust based on network conditions");
  console.log("   • Use manual override only during extreme network congestion");
  console.log("   • Monitor transaction success rates and adjust accordingly");
}

// Run the comparison
if (require.main === module) {
  compareGasCosts();
  showNetworkConditionExamples();
  showOptimizationFeatures();
}

module.exports = {
  calculateTransactionCost,
  compareGasCosts,
  ORIGINAL_SETTINGS,
  OPTIMIZED_SETTINGS
};
