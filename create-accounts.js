const twofactor = require("node-2fa");
const puppeteer = require("puppeteer");
const fs = require("fs");
const { BackWoodApi } = require("./BackWoodApi.js");
const { Keypair } = require("@solana/web3.js");
const bs58 = require("bs58").default;
const useProxy = require("@stableproxy/puppeteer-page-proxy");
const { sleep } = require("./utils");

const init = async () => {
  while (true) {
    const keypair = Keypair.generate();
    console.log(`public key: ${keypair.publicKey.toString()}`);
    console.log(`private key: ${bs58.encode(keypair.secretKey)}`);
    await createAccount(keypair);
  }
};

const bypassLocalStorageOverride = async (page) =>
  page.evaluateOnNewDocument(() => {
    let __ls = localStorage;

    Object.defineProperty(window, "localStorage", {
      writable: false,
      configurable: false,
      value: __ls,
    });
  });

const createAccount = async (keypair) => {
  let proxy = "http://namnguyen:<EMAIL>:32028";

  try {
    await fetch(
      "http://xoay3.rentproxy.xyz:10000/reset?proxy=xoay3.rentproxy.xyz:32028"
    );
    await sleep(20000);
  } catch (e) {
    await fetch(
      "http://xoay3.rentproxy.xyz:10000/reset?proxy=xoay3.rentproxy.xyz:32028"
    );
    await sleep(20000);

    return createAccount(keypair);
  }

  let api = new BackWoodApi({
    baseUrl: "https://launcher.backwoods.gg/v1",
    proxy: proxy,
  });

  await api.authenticate(bs58.encode(keypair.secretKey));
  const apiCookies = api._headers.Cookie;

  const cookies = apiCookies.split(";").map((it) => {
    let cookData = it.split("=");

    return { name: cookData[0], value: cookData[1], domain: ".backwoods.gg" };
  });

  let linkedX = await loopUntilDone(
    linkTwitterAccount,
    "Account Error",
    proxy,
    cookies,
    api
  );

  console.log(linkedX, "linkedX");
  let linkedDiscord = await await loopUntilDone(
    linkDiscordAccount,
    "Token Error",
    proxy,
    cookies,
    api
  );
  console.log(linkedDiscord, "linkedDiscord");

  fs.appendFileSync(
    "./data/accounts-reg.txt",
    `${keypair.publicKey.toString()}|${bs58.encode(
      keypair.secretKey
    )}|${linkedX}|${linkedDiscord}\n`
  );
};

const loopUntilDone = async (callable, errorMess, proxy, cookies, api) => {
  while (true) {
    try {
      linkedX = await callable(proxy, cookies, api);
      if (linkedX) return linkedX;
    } catch (e) {
      if (e.message == errorMess) throw e;
      else {
        await fetch(
          "http://xoay3.rentproxy.xyz:10000/reset?proxy=xoay3.rentproxy.xyz:32028"
        );
        await sleep(20000);
      }
    }
  }
};

const linkTwitterAccount = async (proxy, cookies, api) => {
  let twitterAccounts = fs
    .readFileSync("./data/twitter-data.txt", "utf8")
    .split("\n");

  let twitterAccount = twitterAccounts.shift();

  if (!twitterAccount) {
    throw new Error("Account Error");
  }

  let twitterAccountData = twitterAccount.split("|");
  let usernameTwitter = twitterAccountData[0];
  let passwordTwitter = twitterAccountData[1];
  let tokenTwitter = twitterAccountData[2];
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  await page.setCookie(...cookies);
  await page.setRequestInterception(true);
  page.on("request", async (request) => {
    if (request.resourceType() === "image") {
      return request.abort();
    } else {
      await useProxy(request, proxy);
    }
  });

  try {
    await page.goto("https://launcher.backwoods.gg/v1/auth/twitter", {
      waitUntil: "load",
      timeout: 0,
    });
    await page.waitForSelector('::-p-xpath(//*[@autocomplete="username"])');
    await page
      .locator('::-p-xpath(//*[@autocomplete="username"])')
      .fill(usernameTwitter);

    page.keyboard.press("Enter");
    await page.waitForSelector('::-p-xpath(//*[@name="password"])');
    await page
      .locator('::-p-xpath(//*[@name="password"])')
      .fill(passwordTwitter);
    page.keyboard.press("Enter");
    await page.waitForSelector('::-p-xpath(//*[@inputmode="numeric"])');
    await page
      .locator('::-p-xpath(//*[@inputmode="numeric"])')
      .fill(twofactor.generateToken(tokenTwitter).token);
    page.keyboard.press("Enter");
    await page.waitForSelector(
      '::-p-xpath(//*[@data-testid="OAuth_Consent_Button"])'
    );
    await page
      .locator('::-p-xpath(//*[@data-testid="OAuth_Consent_Button"])')
      .click();
    await page.waitForFunction("window.location.host.contains('backwoods')");
  } catch (e) {
    //
  }
  await browser.close();
  fs.writeFileSync("./data/twitter-data.txt", twitterAccounts.join("\n"));

  let me = await api.me();
  if (me.twitter) {
    return twitterAccount;
  } else {
    fs.appendFileSync("./data/twitter-data-errors.txt", `${twitterAccount}\n`);
    return null;
  }
};

const linkDiscordAccount = async (proxy, cookies, api) => {
  let discordAccounts = fs
    .readFileSync("./data/discord-data.txt", "utf8")
    .split("\n");
  let token = discordAccounts.shift();
  if (!token) {
    throw new Error("Token Error");
  }

  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  await page.setCookie(...cookies);
  await page.setRequestInterception(true);
  page.on("request", async (request) => {
    if (request.resourceType() === "image") {
      return request.abort();
    } else {
      await useProxy(request, proxy);
    }
  });

  //discord link account
  await bypassLocalStorageOverride(page);
  try {
    await page.goto("https://discord.com/company-information");
  } catch (e) {
    await page.goto("https://discord.com/company-information", {
      waitUntil: "load",
      timeout: 0,
    });
  }
  try {
    await page.evaluate((token) => {
      localStorage.setItem("token", `"${token}"`);
    }, token.trim());
    await page.goto("https://launcher.backwoods.gg/v1/auth/discord");

    await page.waitForSelector('::-p-xpath(//*[@type="button"][2])');
    await page.locator('::-p-xpath(//*[@type="button"][2])').click();

    await page.waitForFunction("window.location.host.contains('backwoods')");
  } catch (e) {}

  await browser.close();
  fs.writeFileSync("./data/discord-data.txt", discordAccounts.join("\n"));

  let me = await api.me();
  if (me.discord) {
    return token;
  } else {
    fs.appendFileSync("./data/discord-data-errors.txt", `${token}\n`);
    return null;
  }
};

init();
