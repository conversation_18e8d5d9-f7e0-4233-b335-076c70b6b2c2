const { System } = require("systemjs");
const WebSocket = require("ws");
const BotController = require("./BotController").default;
const GameStatus = require("./BotController").GameStatus;
const { intToBytes, bytesToInt, sleep } = require("./utils");
const { HttpsProxyAgent } = require("https-proxy-agent");
const fs = require("fs");
const { BackWoodApi } = require("./BackWoodApi");
const { PromisePool } = require("@supercharge/promise-pool");
const fetch = require("node-fetch");
const { Telegraf } = require("telegraf");

const bot = new Telegraf("7236972005:AAEIz6rNdCh8Mhltzopj-Sih2nlg7uUFGHg");
lastMessageId = null;

const runBot = async (otp, proxy) => {
  const MessageIdProtocol = (
    await System.import("./game_sources/MessageIdProtocol.js")
  ).default;
  let Proto = (await System.import("./game_sources/proto.js")).default;
  let MessageId = (await System.import("./game_sources/MessageId.js")).default;

  const decodeMessage = (message) => {
    message = new Uint8Array(message);
    let actionCode = bytesToInt(message.slice(0, 2));
    let captcha = bytesToInt(message.slice(2, 4));
    var dataBuff = message.slice(4);
    var result = MessageIdProtocol.get(actionCode).decode(dataBuff);

    return { result, actionCode, captcha };
  };

  const agent = new HttpsProxyAgent(proxy);

  let ws = new WebSocket("wss://s2.backwoods.gg/", {
    headers: {
      host: "s2.backwoods.gg",
      pragma: "no-cache",
      "cache-control": "no-cache",
      "user-agent":
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      origin: "https://game.backwoods.gg",
      "sec-websocket-version": "13",
      "accept-encoding": "gzip, deflate, br, zstd",
      "accept-language": "en-US,en;q=0.9",
    },
    agent,
    // rejectUnauthorized: false,
  });
  ws.binaryType = "arraybuffer";

  let botController = new BotController(
    ws,
    MessageId,
    Proto,
    MessageIdProtocol
  );

  botController.ws.onopen = (e) => {
    botController.LoginLoginSend(otp);
  };

  ws.onmessage = (message) => {
    let results = decodeMessage(message.data);
    // console.log(JSON.stringify(results), "Receive");

    botController.runner(results.actionCode, results);
  };

  // ws.onerror = function (e) {
  //   console.error("WebSocket error:", e);
  // };

  // ws.onclose = function (e) {
  //   console.log("WebSocket closed:", e);
  // };

  return botController;
};

const statusText = (botController, meData, proxy) => {
  return `${(botController.address ?? "").slice(-10)} - ${
    botController.vit
  } - ${
    meData.seasons && meData.seasons.length > 0
      ? meData.seasons[0].pointsGame ?? 0
      : 0
  } - ${botController.wave} - ${
    botController.gameStatus
  } - ${new Date().toLocaleString()}|${new URL(proxy).host}`;
};

const splitEvery = (n, xs, y = []) =>
  xs.length === 0 ? y : splitEvery(n, xs.slice(n), y.concat([xs.slice(0, n)]));

const currentGameStatus = {};
const THREAD = 3;
const proxyAccountSuccess = [];

const setCurrentGameStatus = (currentIndexStatus, status) => {
  currentGameStatus[currentIndexStatus] = status;
};

const resetIp = async (proxy) => {
  await fetch(
    `http://namnguyen1608.rentproxy.xyz:10000/reset?proxy=${
      new URL(proxy).host
    }`
  );
};

const runSingleBot = (privateKey, proxy) => {
  return new Promise(async (resolve, reject) => {
    let backWoodApi = new BackWoodApi({
      baseUrl: "https://launcher.backwoods.gg/v1",
      proxy: proxy,
    });
    await backWoodApi.authenticate(privateKey);
    let otpData = await backWoodApi.launch();
    let meData = await backWoodApi.me();
    let otp = otpData.otp;

    let botController = await runBot(otp, proxy);

    botController.setOnChangeStatus((status) => {
      if (status == GameStatus.VIT_NOT_ENOUGH) {
        resolve(true);
      }
    });

    let statusInterval = 0;
    let lastStatus = null;
    let interval = setInterval(() => {
      setCurrentGameStatus(
        privateKey,
        statusText(botController, meData, proxy)
      );

      if (
        lastStatus != botController.gameStatus ||
        botController.ws.readyState == WebSocket.CONNECTING
      ) {
        lastStatus = botController.gameStatus;
        statusInterval = 0;
      } else {
        statusInterval++;
      }

      if (statusInterval > 180 && botController.gameStatus != GameStatus.PLAY) {
        botController.ws.close();
      }
    }, 1000);

    const onWs = () => {
      let status = botController.gameStatus;
      console.log(status, "status");
      botController.stop();
      clearInterval(interval);
      backWoodApi
        .me()
        .then((meData) => {
          setCurrentGameStatus(
            privateKey,
            statusText(botController, meData, proxy)
          );
        })
        .catch((e) => {});

      if (status != GameStatus.VIT_NOT_ENOUGH) {
        reject(false);
      } else {
        resolve(true);
      }
    };

    botController.ws.onclose = onWs;
    botController.ws.onerror = onWs;
  });
};

const wrapperRunSingleBot = async (privateKey, proxy) => {
  try {
    await runSingleBot(privateKey, proxy);
    // proxyAccountSuccess[proxyIndex]++;
  } catch (e) {
    console.log("re running??", e);
    await wrapperRunSingleBot(privateKey, proxy);
  }
};

const runWithProxy = async (proxyIndex, proxy, accounts) => {
  let index = 0;
  while (index < accounts.length) {
    const accountLeft = accounts.length - index;
    const accountShouldRun = accountLeft < THREAD ? accountLeft : THREAD;
    // if (proxyAccountSuccess[proxyIndex] % THREAD != 0) continue; //wait thread done
    // if (proxyAccountSuccess[proxyIndex] == accounts.length) break; //break because it done for all accounts
    await resetIp(proxy);
    console.log(`Proxy: ${proxyIndex} - Wait 180`);
    await sleep(180000);

    //run bot
    let promiseAll = [];
    const result = [];
    for (let i = 0; i < accountShouldRun; i++) {
      let accountIndex = index + i;
      let privateKey = accounts[accountIndex].split("|")[1];
      result.push(accountIndex);
      console.log(
        `Proxy: ${proxyIndex} - Push promise account ${accountIndex}`
      );
      promiseAll.push(wrapperRunSingleBot(privateKey, proxy));
    }
    index = index + accountShouldRun;

    await Promise.all(promiseAll);
    console.log(`Done: ${proxyIndex} - Accounts ${result.join(",")}`);
  }

  console.log(`Done: ${proxyIndex} - Accounts ${accounts.join(",")}`);
  await runWithProxy(proxyIndex, proxy, accounts);
};

const init2 = async () => {
  let accounts = fs.readFileSync("./data/accounts.txt", "utf8").split("\n");
  let proxies = fs.readFileSync("./data/proxies.txt", "utf8").split("\n");

  let accountHandlePerProxy = Math.floor(accounts.length / proxies.length);
  let chunkAccounts = splitEvery(accountHandlePerProxy, accounts);
  let promiseAll = [];

  setInterval(async () => {
    fs.writeFileSync(
      "running.txt",
      Object.values(currentGameStatus).join("\n")
    );

    // console.clear();
    // console.log(currentGameStatus.join("\n"));
  }, 2000);

  for (let indexProxy = 0; indexProxy < proxies.length; indexProxy++) {
    proxyAccountSuccess[indexProxy] = 0;
    const proxy = proxies[indexProxy]; //ip:porty
    const listAccount = chunkAccounts[indexProxy]; //list acc chunk
    if (!listAccount || listAccount.length == 0) continue;
    promiseAll.push(runWithProxy(indexProxy, proxy, listAccount));
    await sleep(10000);
  }

  await Promise.all(promiseAll);
};

init2();
