const Proxy = require("http-mitm-proxy").Proxy;
// or using import/module (package.json -> "type": "module")
// import { Proxy } from "http-mitm-proxy";

const proxy = new Proxy();

const bytesToInt = (n) => {
  return n[0] | (n[1] << 8);
};

proxy.onWebSocketConnection((ctx, callback) => {
  console.log("WEBSOCKET CONNECT:", ctx.clientToProxyWebSocket.upgradeReq.url);
  //
  return callback();
});
proxy.onWebSocketFrame((ctx, type, fromServer, message, flags, callback) => {
  let messageByte = new Uint8Array(message);
  //   console.log(decodeBinaryMessage(messageByte));
  //   let data = bytesToInt(s.slice(0, 2));
  //   let captcha = bytesToInt(s.slice(2, 4));
  //   //

  //   console.log(
  //     `WEBSOCKET FRAME ${type} received from ${fromServer ? "server" : "client"}`,
  //     ctx.clientToProxyWebSocket.upgradeReq.url,
  //     message
  //   );
  //
  return callback(null, message, flags);
});

console.log("begin listening on 8081");
proxy.listen({ port: 8081, sslCaDir: process.cwd() + "/http-mitm-proxy" });
