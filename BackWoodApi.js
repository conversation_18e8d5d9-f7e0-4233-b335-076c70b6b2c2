const { Keypair } = require("@solana/web3.js");
const bs58 = require("bs58").default;
const nacl = require("tweetnacl");
const { decodeUTF8 } = require("tweetnacl-util");
const { HttpsProxyAgent } = require("https-proxy-agent");
const fetch = require("node-fetch");

class BackWoodApi {
  baseUrl;
  _headers;
  httpsAgent;

  constructor(config) {
    this.baseUrl = config.baseUrl;
    if (config.proxy) this.httpsAgent = new HttpsProxyAgent(config.proxy);
    else this.httpsAgent = null;
    this._headers = {};
  }

  async getLoginMessage(address) {
    const resp = await fetch(`${this.baseUrl}/auth/${address}`, {
      method: "POST",
      body: JSON.stringify({}),
      headers: this.headers(true),
      httpAgent: this.httpsAgent,
    });

    return (await resp.json()).message;
  }

  async authenticate(privateKey) {
    const keypair = Keypair.fromSecretKey(bs58.decode(privateKey));
    const address = keypair.publicKey.toBase58();

    const loginMsg = await this.getLoginMessage(address);
    const msgBytes = decodeUTF8(loginMsg);

    const signatureBytes = nacl.sign.detached(msgBytes, keypair.secretKey);
    const signatureBase58 = bs58.encode(signatureBytes);

    const data = await fetch(`${this.baseUrl}/auth/${address}/verify`, {
      method: "POST",
      body: JSON.stringify({ signature: signatureBase58 }),
      headers: this.headers(true),
      httpAgent: this.httpsAgent,
    });

    this.setCookie(data.headers.get("set-cookie").split(";")[0]);

    return await data.text();
  }
  async setCookie(cookie) {
    let cookies = this.headers.Cookie ?? "";
    cookies += (cookies ? ";" : "") + cookie;
    this._headers = { Cookie: cookies };
  }

  async me() {
    const userInfo = await fetch(`${this.baseUrl}/profile/me`, {
      headers: this.headers(),
      agent: this.httpsAgent,
    });

    return await userInfo.json();
  }

  async claim() {
    const claimInfo = await fetch(`${this.baseUrl}/claim`, {
      headers: this.headers(),
      agent: this.httpsAgent,
    });

    return await claimInfo.json();
  }

  async proof() {
    const claimInfo = await fetch(`${this.baseUrl}/claim/proof`, {
      headers: this.headers(),
      agent: this.httpsAgent,
    });

    return await claimInfo.json();
  }

  async launch() {
    const launchInfo = await fetch(`${this.baseUrl}/launch`, {
      method: "POST",
      body: JSON.stringify({}),
      headers: this.headers(true),
      httpAgent: this.httpsAgent,
    });

    return await launchInfo.json();
  }

  headers = (post = false) => {
    const postHeader = post
      ? {
          Accept: "application/json",
          "Content-Type": "application/json",
        }
      : {};

    return {
      origin: "https://app.backwoods.gg",
      "User-Agent":
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
      ...postHeader,
      ...this._headers,
    };
  };
}

exports.BackWoodApi = BackWoodApi;
